package dao;

import database.DatabaseConnection;
import models.Player;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Data Access Object for Player operations
 */
public class PlayerDAO {
    private static final Logger LOGGER = Logger.getLogger(PlayerDAO.class.getName());
    private DatabaseConnection dbConnection;
    
    public PlayerDAO() {
        this.dbConnection = DatabaseConnection.getInstance();
    }
    
    /**
     * Add a new player to the database
     */
    public boolean addPlayer(Player player) {
        String query = """
            INSERT INTO players (name, country, role, age, base_price, category, special_skills) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """;
        
        try {
            ResultSet rs = dbConnection.executePreparedUpdateWithKeys(query,
                player.getName(), player.getCountry(), player.getRole(),
                player.getAge(), player.getBasePrice(), player.getCategory(),
                player.getSpecialSkills());
            
            if (rs.next()) {
                player.setPlayerId(rs.getInt(1));
                LOGGER.info("Player added successfully: " + player.getName());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error adding player: " + player.getName(), e);
        }
        return false;
    }
    
    /**
     * Get player by ID
     */
    public Player getPlayerById(int playerId) {
        String query = "SELECT * FROM players WHERE player_id = ?";
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, playerId)) {
            if (rs.next()) {
                return mapResultSetToPlayer(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting player by ID: " + playerId, e);
        }
        return null;
    }
    
    /**
     * Get all players
     */
    public List<Player> getAllPlayers() {
        String query = "SELECT * FROM players ORDER BY name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting all players", e);
        }
        return players;
    }
    
    /**
     * Get players by role
     */
    public List<Player> getPlayersByRole(String role) {
        String query = "SELECT * FROM players WHERE role = ? ORDER BY name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, role)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting players by role: " + role, e);
        }
        return players;
    }
    
    /**
     * Get players by country
     */
    public List<Player> getPlayersByCountry(String country) {
        String query = "SELECT * FROM players WHERE country = ? ORDER BY name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, country)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting players by country: " + country, e);
        }
        return players;
    }
    
    /**
     * Get unsold players
     */
    public List<Player> getUnsoldPlayers() {
        String query = "SELECT * FROM players WHERE is_sold = FALSE ORDER BY base_price DESC, name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting unsold players", e);
        }
        return players;
    }
    
    /**
     * Get sold players
     */
    public List<Player> getSoldPlayers() {
        String query = "SELECT * FROM players WHERE is_sold = TRUE ORDER BY sold_price DESC, name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting sold players", e);
        }
        return players;
    }
    
    /**
     * Get players by team
     */
    public List<Player> getPlayersByTeam(int teamId) {
        String query = "SELECT * FROM players WHERE team_id = ? ORDER BY sold_price DESC, name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, teamId)) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting players by team: " + teamId, e);
        }
        return players;
    }
    
    /**
     * Update player information
     */
    public boolean updatePlayer(Player player) {
        String query = """
            UPDATE players SET name = ?, country = ?, role = ?, age = ?, 
            base_price = ?, category = ?, special_skills = ? 
            WHERE player_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query,
                player.getName(), player.getCountry(), player.getRole(),
                player.getAge(), player.getBasePrice(), player.getCategory(),
                player.getSpecialSkills(), player.getPlayerId());
            
            if (rowsAffected > 0) {
                LOGGER.info("Player updated successfully: " + player.getName());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating player: " + player.getName(), e);
        }
        return false;
    }
    
    /**
     * Mark player as sold
     */
    public boolean sellPlayer(int playerId, int teamId, double soldPrice) {
        String query = """
            UPDATE players SET is_sold = TRUE, team_id = ?, sold_price = ? 
            WHERE player_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, teamId, soldPrice, playerId);
            if (rowsAffected > 0) {
                LOGGER.info("Player sold successfully: ID " + playerId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error selling player: ID " + playerId, e);
        }
        return false;
    }
    
    /**
     * Mark player as unsold (reset)
     */
    public boolean unsellPlayer(int playerId) {
        String query = """
            UPDATE players SET is_sold = FALSE, team_id = NULL, sold_price = 0.00 
            WHERE player_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, playerId);
            if (rowsAffected > 0) {
                LOGGER.info("Player marked as unsold: ID " + playerId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error unselling player: ID " + playerId, e);
        }
        return false;
    }
    
    /**
     * Delete player
     */
    public boolean deletePlayer(int playerId) {
        String query = "DELETE FROM players WHERE player_id = ?";
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, playerId);
            if (rowsAffected > 0) {
                LOGGER.info("Player deleted successfully: ID " + playerId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error deleting player: ID " + playerId, e);
        }
        return false;
    }
    
    /**
     * Search players by name
     */
    public List<Player> searchPlayersByName(String name) {
        String query = "SELECT * FROM players WHERE name LIKE ? ORDER BY name";
        List<Player> players = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, "%" + name + "%")) {
            while (rs.next()) {
                players.add(mapResultSetToPlayer(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error searching players by name: " + name, e);
        }
        return players;
    }
    
    /**
     * Map ResultSet to Player object
     */
    private Player mapResultSetToPlayer(ResultSet rs) throws SQLException {
        return new Player(
            rs.getInt("player_id"),
            rs.getString("name"),
            rs.getString("country"),
            rs.getString("role"),
            rs.getInt("age"),
            rs.getDouble("base_price"),
            rs.getString("category"),
            rs.getBoolean("is_sold"),
            rs.getInt("team_id"),
            rs.getDouble("sold_price"),
            rs.getString("special_skills")
        );
    }
}
