import gui.MainFrame;
import javax.swing.*;
import java.awt.*;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Main application class for IPL Auction System
 */
public class IPLAuctionApp {
    private static final Logger LOGGER = Logger.getLogger(IPLAuctionApp.class.getName());
    
    public static void main(String[] args) {
        // Set system properties for better GUI experience
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");
        
        // Configure logging
        configureLogging();
        
        // Show splash screen
        showSplashScreen();
        
        // Initialize and show main application
        SwingUtilities.invokeLater(() -> {
            try {
                // Set look and feel
                setLookAndFeel();
                
                // Create and show main frame
                MainFrame mainFrame = new MainFrame();
                mainFrame.setVisible(true);
                
                LOGGER.info("IPL Auction Application started successfully");
                
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Failed to start application", e);
                showErrorDialog("Application Error", 
                    "Failed to start IPL Auction Application: " + e.getMessage());
                System.exit(1);
            }
        });
    }
    
    /**
     * Configure logging for the application
     */
    private static void configureLogging() {
        // Set root logger level
        Logger.getLogger("").setLevel(Level.INFO);
        
        // You can add file logging here if needed
        LOGGER.info("Logging configured");
    }
    
    /**
     * Set the look and feel for the application
     */
    private static void setLookAndFeel() {
        try {
            // Try to set Nimbus look and feel
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    LOGGER.info("Nimbus look and feel set successfully");
                    return;
                }
            }
            // Fallback to default
            LOGGER.info("Using default look and feel");
        } catch (Exception e) {
            LOGGER.warning("Could not set look and feel: " + e.getMessage());
        }
        
        // Customize UI defaults
        customizeUIDefaults();
    }
    
    /**
     * Customize UI defaults for better appearance
     */
    private static void customizeUIDefaults() {
        // Set default font
        Font defaultFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
        
        UIManager.put("Button.font", defaultFont);
        UIManager.put("Label.font", defaultFont);
        UIManager.put("TextField.font", defaultFont);
        UIManager.put("TextArea.font", defaultFont);
        UIManager.put("Table.font", defaultFont);
        UIManager.put("TableHeader.font", defaultFont.deriveFont(Font.BOLD));
        UIManager.put("Menu.font", defaultFont);
        UIManager.put("MenuItem.font", defaultFont);
        UIManager.put("TabbedPane.font", defaultFont);
        
        // Set colors
        UIManager.put("Table.selectionBackground", new Color(184, 207, 229));
        UIManager.put("Table.selectionForeground", Color.BLACK);
        
        LOGGER.info("UI defaults customized");
    }
    
    /**
     * Show splash screen while application loads
     */
    private static void showSplashScreen() {
        JWindow splash = new JWindow();
        splash.setSize(400, 250);
        splash.setLocationRelativeTo(null);
        
        // Create splash content
        JPanel splashPanel = new JPanel(new BorderLayout());
        splashPanel.setBorder(BorderFactory.createRaisedBevelBorder());
        splashPanel.setBackground(Color.WHITE);
        
        // Title
        JLabel titleLabel = new JLabel("IPL Auction System", JLabel.CENTER);
        titleLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 24));
        titleLabel.setForeground(new Color(0, 102, 204));
        titleLabel.setBorder(BorderFactory.createEmptyBorder(20, 0, 10, 0));
        
        // Subtitle
        JLabel subtitleLabel = new JLabel("Professional Cricket Auction Management", JLabel.CENTER);
        subtitleLabel.setFont(new Font(Font.SANS_SERIF, Font.ITALIC, 14));
        subtitleLabel.setForeground(Color.DARK_GRAY);
        
        // Version info
        JLabel versionLabel = new JLabel("Version 1.0", JLabel.CENTER);
        versionLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 12));
        versionLabel.setForeground(Color.GRAY);
        
        // Loading message
        JLabel loadingLabel = new JLabel("Loading...", JLabel.CENTER);
        loadingLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 12));
        loadingLabel.setBorder(BorderFactory.createEmptyBorder(10, 0, 20, 0));
        
        // Progress bar
        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setBorder(BorderFactory.createEmptyBorder(0, 20, 20, 20));
        
        // Add components
        JPanel centerPanel = new JPanel(new GridLayout(4, 1));
        centerPanel.setBackground(Color.WHITE);
        centerPanel.add(titleLabel);
        centerPanel.add(subtitleLabel);
        centerPanel.add(versionLabel);
        centerPanel.add(loadingLabel);
        
        splashPanel.add(centerPanel, BorderLayout.CENTER);
        splashPanel.add(progressBar, BorderLayout.SOUTH);
        
        splash.add(splashPanel);
        splash.setVisible(true);
        
        // Show splash for 2 seconds
        Timer timer = new Timer(2000, e -> splash.dispose());
        timer.setRepeats(false);
        timer.start();
        
        try {
            Thread.sleep(2100); // Wait for splash to close
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Show error dialog
     */
    private static void showErrorDialog(String title, String message) {
        JOptionPane.showMessageDialog(null, message, title, JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * Get application information
     */
    public static String getApplicationInfo() {
        return """
            IPL Auction System v1.0
            
            A comprehensive auction management system for IPL cricket teams.
            
            Features:
            • Player Management - Add, edit, and manage player profiles
            • Team Management - Manage team information and budgets
            • Live Auction - Conduct real-time player auctions
            • Results & Analytics - View detailed auction results and statistics
            
            Technology Stack:
            • Java Swing for GUI
            • MySQL for database
            • JDBC for database connectivity
            
            Developed with modern software engineering practices.
            """;
    }
    
    /**
     * Get system requirements
     */
    public static String getSystemRequirements() {
        return """
            System Requirements:
            
            Minimum:
            • Java 11 or higher
            • MySQL 8.0 or higher
            • 2 GB RAM
            • 100 MB disk space
            
            Recommended:
            • Java 17 or higher
            • MySQL 8.0 or higher
            • 4 GB RAM
            • 500 MB disk space
            • 1920x1080 screen resolution
            
            Database Configuration:
            • MySQL server running on localhost:3306
            • Database user with CREATE, INSERT, UPDATE, DELETE privileges
            • Update database credentials in DatabaseConfig.java
            """;
    }
}
