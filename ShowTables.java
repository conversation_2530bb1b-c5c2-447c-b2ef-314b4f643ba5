import java.sql.*;

public class ShowTables {
    public static void main(String[] args) {
        String url = "*******************************";
        String username = "root";
        String password = "M@noj4188";
        
        try {
            Connection conn = DriverManager.getConnection(url, username, password);
            Statement stmt = conn.createStatement();
            
            System.out.println("=== IPL AUCTION DATABASE DEMONSTRATION ===\n");
            
            // Show all tables
            System.out.println("1. TABLES IN DATABASE:");
            ResultSet tables = stmt.executeQuery("SHOW TABLES");
            while (tables.next()) {
                System.out.println("   - " + tables.getString(1));
            }
            
            // Show teams table
            System.out.println("\n2. TEAMS TABLE:");
            System.out.println("   ID | Team Name                    | City      | Budget");
            System.out.println("   ---|------------------------------|-----------|--------");
            ResultSet teams = stmt.executeQuery("SELECT team_id, team_name, city, budget FROM teams");
            while (teams.next()) {
                System.out.printf("   %-2d | %-28s | %-9s | ₹%.0f Cr%n", 
                    teams.getInt("team_id"),
                    teams.getString("team_name"),
                    teams.getString("city"),
                    teams.getDouble("budget"));
            }
            
            // Show players table (sample)
            System.out.println("\n3. PLAYERS TABLE (Sample):");
            System.out.println("   ID | Player Name      | Country   | Role         | Base Price");
            System.out.println("   ---|------------------|-----------|--------------|------------");
            ResultSet players = stmt.executeQuery("SELECT player_id, name, country, role, base_price FROM players LIMIT 8");
            while (players.next()) {
                System.out.printf("   %-2d | %-16s | %-9s | %-12s | ₹%.1f Cr%n",
                    players.getInt("player_id"),
                    players.getString("name"),
                    players.getString("country"),
                    players.getString("role"),
                    players.getDouble("base_price"));
            }
            
            // Show table structures
            System.out.println("\n4. TABLE STRUCTURES:");
            
            String[] tableNames = {"teams", "players", "auctions", "bids"};
            for (String tableName : tableNames) {
                System.out.println("\n   " + tableName.toUpperCase() + " table structure:");
                ResultSet columns = stmt.executeQuery("DESCRIBE " + tableName);
                while (columns.next()) {
                    System.out.printf("     %-20s | %-15s | %s%n",
                        columns.getString("Field"),
                        columns.getString("Type"),
                        columns.getString("Key"));
                }
            }
            
            // Show relationships
            System.out.println("\n5. TABLE RELATIONSHIPS:");
            System.out.println("   - players.team_id → teams.team_id");
            System.out.println("   - bids.auction_id → auctions.auction_id");
            System.out.println("   - bids.player_id → players.player_id");
            System.out.println("   - bids.team_id → teams.team_id");
            
            conn.close();
            System.out.println("\n=== DEMONSTRATION COMPLETE ===");
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
        }
    }
}
