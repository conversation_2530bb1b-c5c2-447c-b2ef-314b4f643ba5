package dao;

import database.DatabaseConnection;
import models.Auction;
import models.Bid;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Data Access Object for Auction and Bid operations
 */
public class AuctionDAO {
    private static final Logger LOGGER = Logger.getLogger(AuctionDAO.class.getName());
    private DatabaseConnection dbConnection;
    
    public AuctionDAO() {
        this.dbConnection = DatabaseConnection.getInstance();
    }
    
    // ========== AUCTION OPERATIONS ==========
    
    /**
     * Create a new auction
     */
    public boolean createAuction(Auction auction) {
        String query = """
            INSERT INTO auctions (auction_name, auction_type, description, status) 
            VALUES (?, ?, ?, ?)
            """;
        
        try {
            ResultSet rs = dbConnection.executePreparedUpdateWithKeys(query,
                auction.getAuctionName(), auction.getAuctionType(), 
                auction.getDescription(), auction.getStatus());
            
            if (rs.next()) {
                auction.setAuctionId(rs.getInt(1));
                LOGGER.info("Auction created successfully: " + auction.getAuctionName());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error creating auction: " + auction.getAuctionName(), e);
        }
        return false;
    }
    
    /**
     * Get auction by ID
     */
    public Auction getAuctionById(int auctionId) {
        String query = "SELECT * FROM auctions WHERE auction_id = ?";
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, auctionId)) {
            if (rs.next()) {
                return mapResultSetToAuction(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting auction by ID: " + auctionId, e);
        }
        return null;
    }
    
    /**
     * Get all auctions
     */
    public List<Auction> getAllAuctions() {
        String query = "SELECT * FROM auctions ORDER BY created_at DESC";
        List<Auction> auctions = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                auctions.add(mapResultSetToAuction(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting all auctions", e);
        }
        return auctions;
    }
    
    /**
     * Get active auction
     */
    public Auction getActiveAuction() {
        String query = "SELECT * FROM auctions WHERE status = 'ACTIVE' LIMIT 1";
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            if (rs.next()) {
                return mapResultSetToAuction(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting active auction", e);
        }
        return null;
    }
    
    /**
     * Update auction status
     */
    public boolean updateAuctionStatus(int auctionId, String status) {
        String query = "UPDATE auctions SET status = ? WHERE auction_id = ?";
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, status, auctionId);
            if (rowsAffected > 0) {
                LOGGER.info("Auction status updated: " + auctionId + " -> " + status);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating auction status: " + auctionId, e);
        }
        return false;
    }
    
    /**
     * Update current player in auction
     */
    public boolean updateCurrentPlayer(int auctionId, int playerId, double currentBid) {
        String query = """
            UPDATE auctions SET current_player_id = ?, current_bid = ?, 
            current_bidding_team_id = NULL WHERE auction_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, playerId, currentBid, auctionId);
            if (rowsAffected > 0) {
                LOGGER.info("Current player updated in auction: " + auctionId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating current player in auction: " + auctionId, e);
        }
        return false;
    }
    
    /**
     * Update current bid in auction
     */
    public boolean updateCurrentBid(int auctionId, double bidAmount, int biddingTeamId) {
        String query = """
            UPDATE auctions SET current_bid = ?, current_bidding_team_id = ? 
            WHERE auction_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, bidAmount, biddingTeamId, auctionId);
            if (rowsAffected > 0) {
                LOGGER.info("Current bid updated in auction: " + auctionId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating current bid in auction: " + auctionId, e);
        }
        return false;
    }
    
    // ========== BID OPERATIONS ==========
    
    /**
     * Place a bid
     */
    public boolean placeBid(Bid bid) {
        String query = """
            INSERT INTO bids (auction_id, player_id, team_id, bid_amount, bid_type, bid_status) 
            VALUES (?, ?, ?, ?, ?, ?)
            """;
        
        try {
            // First, mark all previous bids for this player as OUTBID
            markPreviousBidsAsOutbid(bid.getAuctionId(), bid.getPlayerId());
            
            // Insert the new bid
            ResultSet rs = dbConnection.executePreparedUpdateWithKeys(query,
                bid.getAuctionId(), bid.getPlayerId(), bid.getTeamId(),
                bid.getBidAmount(), bid.getBidType(), bid.getBidStatus());
            
            if (rs.next()) {
                bid.setBidId(rs.getInt(1));
                
                // Update the auction's current bid
                updateCurrentBid(bid.getAuctionId(), bid.getBidAmount(), bid.getTeamId());
                
                LOGGER.info("Bid placed successfully: " + bid.getBidAmount() + " for player " + bid.getPlayerId());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error placing bid", e);
        }
        return false;
    }
    
    /**
     * Get all bids for a player in an auction
     */
    public List<Bid> getBidsForPlayer(int auctionId, int playerId) {
        String query = """
            SELECT * FROM bids WHERE auction_id = ? AND player_id = ? 
            ORDER BY bid_amount DESC, bid_time DESC
            """;
        List<Bid> bids = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, auctionId, playerId)) {
            while (rs.next()) {
                bids.add(mapResultSetToBid(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting bids for player: " + playerId, e);
        }
        return bids;
    }
    
    /**
     * Get highest bid for a player
     */
    public Bid getHighestBidForPlayer(int auctionId, int playerId) {
        String query = """
            SELECT * FROM bids WHERE auction_id = ? AND player_id = ? 
            ORDER BY bid_amount DESC, bid_time DESC LIMIT 1
            """;
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, auctionId, playerId)) {
            if (rs.next()) {
                return mapResultSetToBid(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting highest bid for player: " + playerId, e);
        }
        return null;
    }
    
    /**
     * Get all bids for an auction
     */
    public List<Bid> getBidsForAuction(int auctionId) {
        String query = "SELECT * FROM bids WHERE auction_id = ? ORDER BY bid_time DESC";
        List<Bid> bids = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, auctionId)) {
            while (rs.next()) {
                bids.add(mapResultSetToBid(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting bids for auction: " + auctionId, e);
        }
        return bids;
    }
    
    /**
     * Mark previous bids as outbid
     */
    private void markPreviousBidsAsOutbid(int auctionId, int playerId) throws SQLException {
        String query = """
            UPDATE bids SET bid_status = 'OUTBID' 
            WHERE auction_id = ? AND player_id = ? AND bid_status = 'ACTIVE'
            """;
        
        dbConnection.executePreparedUpdate(query, auctionId, playerId);
    }
    
    /**
     * Mark winning bid
     */
    public boolean markWinningBid(int auctionId, int playerId) {
        String query = """
            UPDATE bids SET bid_status = 'WINNING' 
            WHERE auction_id = ? AND player_id = ? AND bid_status = 'ACTIVE'
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, auctionId, playerId);
            if (rowsAffected > 0) {
                LOGGER.info("Winning bid marked for player: " + playerId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error marking winning bid for player: " + playerId, e);
        }
        return false;
    }
    
    /**
     * Map ResultSet to Auction object
     */
    private Auction mapResultSetToAuction(ResultSet rs) throws SQLException {
        Auction auction = new Auction(
            rs.getInt("auction_id"),
            rs.getString("auction_name"),
            rs.getTimestamp("start_time") != null ? rs.getTimestamp("start_time").toLocalDateTime() : null,
            rs.getTimestamp("end_time") != null ? rs.getTimestamp("end_time").toLocalDateTime() : null,
            rs.getString("status"),
            rs.getInt("current_player_id"),
            rs.getDouble("current_bid"),
            rs.getInt("current_bidding_team_id"),
            rs.getString("auction_type"),
            rs.getString("description")
        );
        return auction;
    }
    
    /**
     * Map ResultSet to Bid object
     */
    private Bid mapResultSetToBid(ResultSet rs) throws SQLException {
        return new Bid(
            rs.getInt("bid_id"),
            rs.getInt("auction_id"),
            rs.getInt("player_id"),
            rs.getInt("team_id"),
            rs.getDouble("bid_amount"),
            rs.getTimestamp("bid_time").toLocalDateTime(),
            rs.getString("bid_status"),
            rs.getString("bid_type")
        );
    }
}
