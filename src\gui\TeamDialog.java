package gui;

import config.DatabaseConfig;
import models.Team;
import services.AuctionService;
import javax.swing.*;
import java.awt.*;

/**
 * Dialog for adding/editing team information
 */
public class TeamDialog extends JDialog {
    private AuctionService auctionService;
    private Team team;
    private boolean confirmed = false;
    
    // Form components
    private JTextField nameField;
    private JTextField cityField;
    private JTextField ownerField;
    private JSpinner budgetSpinner;
    private JSpinner maxPlayersSpinner;
    private JTextField coachField;
    private JTextField captainField;
    
    public TeamDialog(JFrame parent, String title, Team team, AuctionService auctionService) {
        super(parent, title, true);
        this.auctionService = auctionService;
        this.team = team;
        
        initializeComponents();
        setupLayout();
        
        if (team != null) {
            populateFields();
        }
        
        setSize(400, 350);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }
    
    /**
     * Initialize form components
     */
    private void initializeComponents() {
        nameField = new JTextField(20);
        cityField = new JTextField(20);
        ownerField = new JTextField(20);
        
        budgetSpinner = new JSpinner(new SpinnerNumberModel(
            DatabaseConfig.DEFAULT_TEAM_BUDGET, 50.0, 200.0, 5.0));
        
        maxPlayersSpinner = new JSpinner(new SpinnerNumberModel(
            DatabaseConfig.DEFAULT_MAX_PLAYERS, 20, 30, 1));
        
        coachField = new JTextField(20);
        captainField = new JTextField(20);
    }
    
    /**
     * Setup dialog layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create form panel
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Team Name
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Team Name:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(nameField, gbc);
        
        // City
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("City:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(cityField, gbc);
        
        // Owner
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Owner:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(ownerField, gbc);
        
        // Budget
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Budget (Cr):*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(budgetSpinner, gbc);
        
        // Max Players
        gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Max Players:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(maxPlayersSpinner, gbc);
        
        // Coach
        gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Coach:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(coachField, gbc);
        
        // Captain
        gbc.gridx = 0; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Captain:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(captainField, gbc);
        
        add(formPanel, BorderLayout.CENTER);
        
        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton saveButton = new JButton(team == null ? "Add Team" : "Update Team");
        JButton cancelButton = new JButton("Cancel");
        
        saveButton.addActionListener(e -> saveTeam());
        cancelButton.addActionListener(e -> dispose());
        
        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
        
        // Add required field note
        JLabel noteLabel = new JLabel("* Required fields");
        noteLabel.setFont(noteLabel.getFont().deriveFont(Font.ITALIC));
        noteLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(noteLabel, BorderLayout.NORTH);
        
        // Set default button
        getRootPane().setDefaultButton(saveButton);
    }
    
    /**
     * Populate fields with existing team data
     */
    private void populateFields() {
        nameField.setText(team.getTeamName());
        cityField.setText(team.getCity());
        ownerField.setText(team.getOwner());
        budgetSpinner.setValue(team.getBudget());
        maxPlayersSpinner.setValue(team.getMaxPlayers());
        
        if (team.getCoach() != null) {
            coachField.setText(team.getCoach());
        }
        if (team.getCaptain() != null) {
            captainField.setText(team.getCaptain());
        }
    }
    
    /**
     * Validate form input
     */
    private boolean validateInput() {
        // Check required fields
        if (nameField.getText().trim().isEmpty()) {
            showError("Please enter team name");
            nameField.requestFocus();
            return false;
        }
        
        if (cityField.getText().trim().isEmpty()) {
            showError("Please enter city");
            cityField.requestFocus();
            return false;
        }
        
        // Validate budget
        double budget = (Double) budgetSpinner.getValue();
        if (budget < 50.0 || budget > 200.0) {
            showError("Budget must be between 50 and 200 Crores");
            budgetSpinner.requestFocus();
            return false;
        }
        
        // Validate max players
        int maxPlayers = (Integer) maxPlayersSpinner.getValue();
        if (maxPlayers < 20 || maxPlayers > 30) {
            showError("Max players must be between 20 and 30");
            maxPlayersSpinner.requestFocus();
            return false;
        }
        
        // Check for duplicate team name (only for new teams)
        if (team == null) {
            Team existingTeam = auctionService.getTeamDAO().getTeamByName(nameField.getText().trim());
            if (existingTeam != null) {
                showError("Team name already exists");
                nameField.requestFocus();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Save team data
     */
    private void saveTeam() {
        if (!validateInput()) {
            return;
        }
        
        try {
            String name = nameField.getText().trim();
            String city = cityField.getText().trim();
            String owner = ownerField.getText().trim();
            double budget = (Double) budgetSpinner.getValue();
            int maxPlayers = (Integer) maxPlayersSpinner.getValue();
            String coach = coachField.getText().trim();
            String captain = captainField.getText().trim();
            
            boolean success;
            
            if (team == null) {
                // Add new team
                Team newTeam = new Team(name, city, owner, budget);
                newTeam.setMaxPlayers(maxPlayers);
                newTeam.setCoach(coach.isEmpty() ? null : coach);
                newTeam.setCaptain(captain.isEmpty() ? null : captain);
                success = auctionService.getTeamDAO().addTeam(newTeam);
            } else {
                // Update existing team
                team.setTeamName(name);
                team.setCity(city);
                team.setOwner(owner);
                team.setBudget(budget);
                team.setMaxPlayers(maxPlayers);
                team.setCoach(coach.isEmpty() ? null : coach);
                team.setCaptain(captain.isEmpty() ? null : captain);
                
                // If budget changed, update remaining budget proportionally
                if (team.getBudget() != budget) {
                    double ratio = budget / team.getBudget();
                    team.setRemainingBudget(team.getRemainingBudget() * ratio);
                    team.setBudget(budget);
                }
                
                success = auctionService.getTeamDAO().updateTeam(team);
            }
            
            if (success) {
                confirmed = true;
                dispose();
            } else {
                showError("Failed to save team data");
            }
            
        } catch (Exception e) {
            showError("Error saving team: " + e.getMessage());
        }
    }
    
    /**
     * Show error message
     */
    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Input Error", JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * Check if dialog was confirmed
     */
    public boolean isConfirmed() {
        return confirmed;
    }
}
