package gui;

import config.DatabaseConfig;
import models.Player;
import services.AuctionService;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Panel for managing players in the IPL auction system
 */
public class PlayerManagementPanel extends JPanel {
    private AuctionService auctionService;
    private JTable playerTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> roleFilter;
    private JComboBox<String> countryFilter;
    private JComboBox<String> statusFilter;
    
    public PlayerManagementPanel(AuctionService auctionService) {
        this.auctionService = auctionService;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        refreshData();
    }
    
    /**
     * Initialize GUI components
     */
    private void initializeComponents() {
        // Create table model
        String[] columnNames = {"ID", "Name", "Country", "Role", "Age", "Base Price (Cr)", 
                               "Category", "Status", "Team", "Sold Price (Cr)"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make table read-only
            }
        };
        
        // Create table
        playerTable = new JTable(tableModel);
        playerTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        playerTable.setRowHeight(25);
        playerTable.getTableHeader().setReorderingAllowed(false);
        
        // Create search components
        searchField = new JTextField(20);
        
        // Create filter components
        roleFilter = new JComboBox<>();
        roleFilter.addItem("All Roles");
        for (String role : DatabaseConfig.PLAYER_ROLES) {
            roleFilter.addItem(role);
        }
        
        countryFilter = new JComboBox<>();
        countryFilter.addItem("All Countries");
        for (String country : DatabaseConfig.COUNTRIES) {
            countryFilter.addItem(country);
        }
        
        statusFilter = new JComboBox<>(new String[]{"All Players", "Sold", "Unsold"});
    }
    
    /**
     * Setup panel layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create top panel with search and filters
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        topPanel.setBorder(BorderFactory.createTitledBorder("Search & Filter"));
        
        topPanel.add(new JLabel("Search:"));
        topPanel.add(searchField);
        topPanel.add(Box.createHorizontalStrut(10));
        
        topPanel.add(new JLabel("Role:"));
        topPanel.add(roleFilter);
        topPanel.add(Box.createHorizontalStrut(10));
        
        topPanel.add(new JLabel("Country:"));
        topPanel.add(countryFilter);
        topPanel.add(Box.createHorizontalStrut(10));
        
        topPanel.add(new JLabel("Status:"));
        topPanel.add(statusFilter);
        
        JButton searchButton = new JButton("Search");
        JButton clearButton = new JButton("Clear");
        topPanel.add(Box.createHorizontalStrut(10));
        topPanel.add(searchButton);
        topPanel.add(clearButton);
        
        add(topPanel, BorderLayout.NORTH);
        
        // Create center panel with table
        JScrollPane scrollPane = new JScrollPane(playerTable);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Players"));
        add(scrollPane, BorderLayout.CENTER);
        
        // Create bottom panel with buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton addButton = new JButton("Add Player");
        JButton editButton = new JButton("Edit Player");
        JButton deleteButton = new JButton("Delete Player");
        JButton viewButton = new JButton("View Details");
        JButton refreshButton = new JButton("Refresh");
        
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(viewButton);
        buttonPanel.add(refreshButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
        
        // Setup button actions
        searchButton.addActionListener(e -> performSearch());
        clearButton.addActionListener(e -> clearFilters());
        addButton.addActionListener(e -> showAddPlayerDialog());
        editButton.addActionListener(e -> showEditPlayerDialog());
        deleteButton.addActionListener(e -> deleteSelectedPlayer());
        viewButton.addActionListener(e -> showPlayerDetails());
        refreshButton.addActionListener(e -> refreshData());
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Double-click to view details
        playerTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    showPlayerDetails();
                }
            }
        });
        
        // Enter key in search field
        searchField.addActionListener(e -> performSearch());
    }
    
    /**
     * Refresh player data
     */
    public void refreshData() {
        List<Player> players = auctionService.getPlayerDAO().getAllPlayers();
        updateTable(players);
    }
    
    /**
     * Update table with player data
     */
    private void updateTable(List<Player> players) {
        tableModel.setRowCount(0);
        
        for (Player player : players) {
            Object[] row = {
                player.getPlayerId(),
                player.getName(),
                player.getCountry(),
                player.getRole(),
                player.getAge(),
                player.getBasePrice(),
                player.getCategory(),
                player.isSold() ? "SOLD" : "UNSOLD",
                player.isSold() ? getTeamName(player.getTeamId()) : "-",
                player.isSold() ? player.getSoldPrice() : "-"
            };
            tableModel.addRow(row);
        }
    }
    
    /**
     * Get team name by ID
     */
    private String getTeamName(int teamId) {
        if (teamId <= 0) return "-";
        var team = auctionService.getTeamDAO().getTeamById(teamId);
        return team != null ? team.getTeamName() : "Unknown";
    }
    
    /**
     * Perform search based on filters
     */
    private void performSearch() {
        String searchText = searchField.getText().trim();
        String selectedRole = (String) roleFilter.getSelectedItem();
        String selectedCountry = (String) countryFilter.getSelectedItem();
        String selectedStatus = (String) statusFilter.getSelectedItem();
        
        List<Player> players;
        
        // Start with all players or search by name
        if (searchText.isEmpty()) {
            players = auctionService.getPlayerDAO().getAllPlayers();
        } else {
            players = auctionService.getPlayerDAO().searchPlayersByName(searchText);
        }
        
        // Apply filters
        players = players.stream()
            .filter(p -> "All Roles".equals(selectedRole) || p.getRole().equals(selectedRole))
            .filter(p -> "All Countries".equals(selectedCountry) || p.getCountry().equals(selectedCountry))
            .filter(p -> {
                switch (selectedStatus) {
                    case "Sold": return p.isSold();
                    case "Unsold": return !p.isSold();
                    default: return true;
                }
            })
            .toList();
        
        updateTable(players);
    }
    
    /**
     * Clear all filters
     */
    private void clearFilters() {
        searchField.setText("");
        roleFilter.setSelectedIndex(0);
        countryFilter.setSelectedIndex(0);
        statusFilter.setSelectedIndex(0);
        refreshData();
    }
    
    /**
     * Show add player dialog
     */
    private void showAddPlayerDialog() {
        PlayerDialog dialog = new PlayerDialog(
            (JFrame) SwingUtilities.getWindowAncestor(this), 
            "Add New Player", null, auctionService);
        dialog.setVisible(true);
        
        if (dialog.isConfirmed()) {
            refreshData();
        }
    }
    
    /**
     * Show edit player dialog
     */
    private void showEditPlayerDialog() {
        int selectedRow = playerTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a player to edit");
            return;
        }
        
        int playerId = (Integer) tableModel.getValueAt(selectedRow, 0);
        Player player = auctionService.getPlayerDAO().getPlayerById(playerId);
        
        if (player != null) {
            PlayerDialog dialog = new PlayerDialog(
                (JFrame) SwingUtilities.getWindowAncestor(this), 
                "Edit Player", player, auctionService);
            dialog.setVisible(true);
            
            if (dialog.isConfirmed()) {
                refreshData();
            }
        }
    }
    
    /**
     * Delete selected player
     */
    private void deleteSelectedPlayer() {
        int selectedRow = playerTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a player to delete");
            return;
        }
        
        int playerId = (Integer) tableModel.getValueAt(selectedRow, 0);
        String playerName = (String) tableModel.getValueAt(selectedRow, 1);
        
        int result = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to delete player: " + playerName + "?",
            "Confirm Delete", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (auctionService.getPlayerDAO().deletePlayer(playerId)) {
                JOptionPane.showMessageDialog(this, "Player deleted successfully");
                refreshData();
            } else {
                JOptionPane.showMessageDialog(this, "Failed to delete player", 
                    "Error", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Show player details
     */
    private void showPlayerDetails() {
        int selectedRow = playerTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a player to view details");
            return;
        }
        
        int playerId = (Integer) tableModel.getValueAt(selectedRow, 0);
        Player player = auctionService.getPlayerDAO().getPlayerById(playerId);
        
        if (player != null) {
            JDialog dialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(this), 
                "Player Details", true);
            dialog.setSize(400, 300);
            dialog.setLocationRelativeTo(this);
            
            JTextArea textArea = new JTextArea(player.getDetailedInfo());
            textArea.setEditable(false);
            textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            dialog.add(new JScrollPane(textArea));
            dialog.setVisible(true);
        }
    }
}
