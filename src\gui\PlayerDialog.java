package gui;

import config.DatabaseConfig;
import models.Player;
import services.AuctionService;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Dialog for adding/editing player information
 */
public class PlayerDialog extends JDialog {
    private AuctionService auctionService;
    private Player player;
    private boolean confirmed = false;
    
    // Form components
    private JTextField nameField;
    private JComboBox<String> countryCombo;
    private JComboBox<String> roleCombo;
    private JSpinner ageSpinner;
    private JSpinner basePriceSpinner;
    private JComboBox<String> categoryCombo;
    private JTextArea skillsArea;
    
    public PlayerDialog(JFrame parent, String title, Player player, AuctionService auctionService) {
        super(parent, title, true);
        this.auctionService = auctionService;
        this.player = player;
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        if (player != null) {
            populateFields();
        }
        
        setSize(450, 400);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }
    
    /**
     * Initialize form components
     */
    private void initializeComponents() {
        nameField = new JTextField(20);
        
        countryCombo = new JComboBox<>(DatabaseConfig.COUNTRIES);
        countryCombo.setEditable(true);
        
        roleCombo = new JComboBox<>(DatabaseConfig.PLAYER_ROLES);
        
        ageSpinner = new JSpinner(new SpinnerNumberModel(25, 16, 45, 1));
        
        basePriceSpinner = new JSpinner(new SpinnerNumberModel(0.5, 0.2, 20.0, 0.25));
        
        categoryCombo = new JComboBox<>(DatabaseConfig.PLAYER_CATEGORIES);
        
        skillsArea = new JTextArea(3, 20);
        skillsArea.setLineWrap(true);
        skillsArea.setWrapStyleWord(true);
    }
    
    /**
     * Setup dialog layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create form panel
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Name
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Name:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(nameField, gbc);
        
        // Country
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Country:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(countryCombo, gbc);
        
        // Role
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Role:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(roleCombo, gbc);
        
        // Age
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Age:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(ageSpinner, gbc);
        
        // Base Price
        gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Base Price (Cr):*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(basePriceSpinner, gbc);
        
        // Category
        gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        formPanel.add(new JLabel("Category:*"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        formPanel.add(categoryCombo, gbc);
        
        // Special Skills
        gbc.gridx = 0; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.NORTHWEST;
        formPanel.add(new JLabel("Special Skills:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0; gbc.weighty = 1.0;
        formPanel.add(new JScrollPane(skillsArea), gbc);
        
        add(formPanel, BorderLayout.CENTER);
        
        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton saveButton = new JButton(player == null ? "Add Player" : "Update Player");
        JButton cancelButton = new JButton("Cancel");
        
        saveButton.addActionListener(e -> savePlayer());
        cancelButton.addActionListener(e -> dispose());
        
        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);
        
        add(buttonPanel, BorderLayout.SOUTH);
        
        // Add required field note
        JLabel noteLabel = new JLabel("* Required fields");
        noteLabel.setFont(noteLabel.getFont().deriveFont(Font.ITALIC));
        noteLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(noteLabel, BorderLayout.NORTH);
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Enter key to save - will be set when buttons are created
    }
    
    /**
     * Populate fields with existing player data
     */
    private void populateFields() {
        nameField.setText(player.getName());
        countryCombo.setSelectedItem(player.getCountry());
        roleCombo.setSelectedItem(player.getRole());
        ageSpinner.setValue(player.getAge());
        basePriceSpinner.setValue(player.getBasePrice());
        categoryCombo.setSelectedItem(player.getCategory());
        
        if (player.getSpecialSkills() != null) {
            skillsArea.setText(player.getSpecialSkills());
        }
    }
    
    /**
     * Validate form input
     */
    private boolean validateInput() {
        // Check required fields
        if (nameField.getText().trim().isEmpty()) {
            showError("Please enter player name");
            nameField.requestFocus();
            return false;
        }
        
        if (countryCombo.getSelectedItem() == null || 
            countryCombo.getSelectedItem().toString().trim().isEmpty()) {
            showError("Please select a country");
            countryCombo.requestFocus();
            return false;
        }
        
        if (roleCombo.getSelectedItem() == null) {
            showError("Please select a role");
            roleCombo.requestFocus();
            return false;
        }
        
        if (categoryCombo.getSelectedItem() == null) {
            showError("Please select a category");
            categoryCombo.requestFocus();
            return false;
        }
        
        // Validate age
        int age = (Integer) ageSpinner.getValue();
        if (age < 16 || age > 45) {
            showError("Age must be between 16 and 45");
            ageSpinner.requestFocus();
            return false;
        }
        
        // Validate base price
        double basePrice = (Double) basePriceSpinner.getValue();
        if (basePrice < 0.2 || basePrice > 20.0) {
            showError("Base price must be between 0.2 and 20.0 Crores");
            basePriceSpinner.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * Save player data
     */
    private void savePlayer() {
        if (!validateInput()) {
            return;
        }
        
        try {
            String name = nameField.getText().trim();
            String country = countryCombo.getSelectedItem().toString().trim();
            String role = (String) roleCombo.getSelectedItem();
            int age = (Integer) ageSpinner.getValue();
            double basePrice = (Double) basePriceSpinner.getValue();
            String category = (String) categoryCombo.getSelectedItem();
            String skills = skillsArea.getText().trim();
            
            boolean success;
            
            if (player == null) {
                // Add new player
                Player newPlayer = new Player(name, country, role, age, basePrice, category);
                newPlayer.setSpecialSkills(skills.isEmpty() ? null : skills);
                success = auctionService.getPlayerDAO().addPlayer(newPlayer);
            } else {
                // Update existing player
                player.setName(name);
                player.setCountry(country);
                player.setRole(role);
                player.setAge(age);
                player.setBasePrice(basePrice);
                player.setCategory(category);
                player.setSpecialSkills(skills.isEmpty() ? null : skills);
                success = auctionService.getPlayerDAO().updatePlayer(player);
            }
            
            if (success) {
                confirmed = true;
                dispose();
            } else {
                showError("Failed to save player data");
            }
            
        } catch (Exception e) {
            showError("Error saving player: " + e.getMessage());
        }
    }
    
    /**
     * Show error message
     */
    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "Input Error", JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * Check if dialog was confirmed
     */
    public boolean isConfirmed() {
        return confirmed;
    }
}
