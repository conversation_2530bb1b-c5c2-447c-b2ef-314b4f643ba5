package models;

import java.time.LocalDateTime;

/**
 * Auction model representing an auction session
 */
public class Auction {
    private int auctionId;
    private String auctionName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status; // SCHEDULED, ACTIVE, PAUSED, COMPLETED
    private int currentPlayerId;
    private double currentBid;
    private int currentBiddingTeamId;
    private String auctionType; // PLAYER, RETENTION
    private String description;
    
    // Constructors
    public Auction() {}
    
    public Auction(String auctionName, String auctionType, String description) {
        this.auctionName = auctionName;
        this.auctionType = auctionType;
        this.description = description;
        this.status = "SCHEDULED";
        this.currentBid = 0.0;
        this.currentPlayerId = -1;
        this.currentBiddingTeamId = -1;
    }
    
    public Auction(int auctionId, String auctionName, LocalDateTime startTime, 
                   LocalDateTime endTime, String status, int currentPlayerId, 
                   double currentBid, int currentBiddingTeamId, String auctionType, 
                   String description) {
        this.auctionId = auctionId;
        this.auctionName = auctionName;
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
        this.currentPlayerId = currentPlayerId;
        this.currentBid = currentBid;
        this.currentBiddingTeamId = currentBiddingTeamId;
        this.auctionType = auctionType;
        this.description = description;
    }
    
    // Getters and Setters
    public int getAuctionId() { return auctionId; }
    public void setAuctionId(int auctionId) { this.auctionId = auctionId; }
    
    public String getAuctionName() { return auctionName; }
    public void setAuctionName(String auctionName) { this.auctionName = auctionName; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    
    public LocalDateTime getEndTime() { return endTime; }
    public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public int getCurrentPlayerId() { return currentPlayerId; }
    public void setCurrentPlayerId(int currentPlayerId) { this.currentPlayerId = currentPlayerId; }
    
    public double getCurrentBid() { return currentBid; }
    public void setCurrentBid(double currentBid) { this.currentBid = currentBid; }
    
    public int getCurrentBiddingTeamId() { return currentBiddingTeamId; }
    public void setCurrentBiddingTeamId(int currentBiddingTeamId) { this.currentBiddingTeamId = currentBiddingTeamId; }
    
    public String getAuctionType() { return auctionType; }
    public void setAuctionType(String auctionType) { this.auctionType = auctionType; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    // Business methods
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }
    
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }
    
    public boolean isScheduled() {
        return "SCHEDULED".equals(status);
    }
    
    public boolean isPaused() {
        return "PAUSED".equals(status);
    }
    
    public void startAuction() {
        this.status = "ACTIVE";
        this.startTime = LocalDateTime.now();
    }
    
    public void pauseAuction() {
        if (isActive()) {
            this.status = "PAUSED";
        }
    }
    
    public void resumeAuction() {
        if (isPaused()) {
            this.status = "ACTIVE";
        }
    }
    
    public void completeAuction() {
        this.status = "COMPLETED";
        this.endTime = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return String.format("%s - %s (%s)", auctionName, auctionType, status);
    }
    
    public String getAuctionDetails() {
        StringBuilder details = new StringBuilder();
        details.append("Auction: ").append(auctionName).append("\n");
        details.append("Type: ").append(auctionType).append("\n");
        details.append("Status: ").append(status).append("\n");
        details.append("Description: ").append(description != null ? description : "No description").append("\n");
        if (startTime != null) {
            details.append("Start Time: ").append(startTime).append("\n");
        }
        if (endTime != null) {
            details.append("End Time: ").append(endTime).append("\n");
        }
        if (currentPlayerId > 0) {
            details.append("Current Player ID: ").append(currentPlayerId).append("\n");
            details.append("Current Bid: ₹").append(currentBid).append(" Cr");
        }
        return details.toString();
    }
}
