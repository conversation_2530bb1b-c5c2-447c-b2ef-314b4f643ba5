package gui;

import models.Player;
import models.Team;
import services.AuctionService;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;

/**
 * Panel for displaying auction results and analytics
 */
public class ResultsPanel extends JPanel {
    private AuctionService auctionService;
    
    // Components
    private JTabbedPane resultsTabs;
    private JTable soldPlayersTable;
    private DefaultTableModel soldPlayersModel;
    private JTable unsoldPlayersTable;
    private DefaultTableModel unsoldPlayersModel;
    private JTable teamSummaryTable;
    private DefaultTableModel teamSummaryModel;
    private JTextArea summaryTextArea;
    
    public ResultsPanel(AuctionService auctionService) {
        this.auctionService = auctionService;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        refreshData();
    }
    
    /**
     * Initialize GUI components
     */
    private void initializeComponents() {
        // Sold players table
        String[] soldColumns = {"Player", "Country", "Role", "Age", "Base Price (Cr)", 
                               "Sold Price (Cr)", "Team", "Profit/Loss"};
        soldPlayersModel = new DefaultTableModel(soldColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        soldPlayersTable = new JTable(soldPlayersModel);
        soldPlayersTable.setRowHeight(25);
        soldPlayersTable.setAutoCreateRowSorter(true);
        
        // Unsold players table
        String[] unsoldColumns = {"Player", "Country", "Role", "Age", "Base Price (Cr)", "Category"};
        unsoldPlayersModel = new DefaultTableModel(unsoldColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        unsoldPlayersTable = new JTable(unsoldPlayersModel);
        unsoldPlayersTable.setRowHeight(25);
        unsoldPlayersTable.setAutoCreateRowSorter(true);
        
        // Team summary table
        String[] teamColumns = {"Team", "Players", "Total Spent (Cr)", "Remaining Budget (Cr)", 
                               "Avg Player Price (Cr)", "Most Expensive Player"};
        teamSummaryModel = new DefaultTableModel(teamColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        teamSummaryTable = new JTable(teamSummaryModel);
        teamSummaryTable.setRowHeight(25);
        teamSummaryTable.setAutoCreateRowSorter(true);
        
        // Summary text area
        summaryTextArea = new JTextArea();
        summaryTextArea.setEditable(false);
        summaryTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        // Create tabbed pane
        resultsTabs = new JTabbedPane();
    }
    
    /**
     * Setup panel layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Sold Players Tab
        JPanel soldPanel = new JPanel(new BorderLayout());
        soldPanel.add(new JScrollPane(soldPlayersTable), BorderLayout.CENTER);
        
        JPanel soldButtonPanel = new JPanel(new FlowLayout());
        JButton exportSoldButton = new JButton("Export Sold Players");
        JButton viewSoldStatsButton = new JButton("View Statistics");
        soldButtonPanel.add(exportSoldButton);
        soldButtonPanel.add(viewSoldStatsButton);
        soldPanel.add(soldButtonPanel, BorderLayout.SOUTH);
        
        // Unsold Players Tab
        JPanel unsoldPanel = new JPanel(new BorderLayout());
        unsoldPanel.add(new JScrollPane(unsoldPlayersTable), BorderLayout.CENTER);
        
        JPanel unsoldButtonPanel = new JPanel(new FlowLayout());
        JButton exportUnsoldButton = new JButton("Export Unsold Players");
        unsoldButtonPanel.add(exportUnsoldButton);
        unsoldPanel.add(unsoldButtonPanel, BorderLayout.SOUTH);
        
        // Team Summary Tab
        JPanel teamPanel = new JPanel(new BorderLayout());
        teamPanel.add(new JScrollPane(teamSummaryTable), BorderLayout.CENTER);
        
        JPanel teamButtonPanel = new JPanel(new FlowLayout());
        JButton viewTeamDetailsButton = new JButton("View Team Details");
        JButton compareTeamsButton = new JButton("Compare Teams");
        teamButtonPanel.add(viewTeamDetailsButton);
        teamButtonPanel.add(compareTeamsButton);
        teamPanel.add(teamButtonPanel, BorderLayout.SOUTH);
        
        // Overall Summary Tab
        JPanel summaryPanel = new JPanel(new BorderLayout());
        summaryPanel.add(new JScrollPane(summaryTextArea), BorderLayout.CENTER);
        
        JPanel summaryButtonPanel = new JPanel(new FlowLayout());
        JButton refreshSummaryButton = new JButton("Refresh Summary");
        JButton exportSummaryButton = new JButton("Export Summary");
        summaryButtonPanel.add(refreshSummaryButton);
        summaryButtonPanel.add(exportSummaryButton);
        summaryPanel.add(summaryButtonPanel, BorderLayout.SOUTH);
        
        // Add tabs
        resultsTabs.addTab("Sold Players", soldPanel);
        resultsTabs.addTab("Unsold Players", unsoldPanel);
        resultsTabs.addTab("Team Summary", teamPanel);
        resultsTabs.addTab("Overall Summary", summaryPanel);
        
        add(resultsTabs, BorderLayout.CENTER);
        
        // Top panel with refresh button
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton refreshAllButton = new JButton("Refresh All Data");
        topPanel.add(refreshAllButton);
        add(topPanel, BorderLayout.NORTH);
        
        // Setup button actions
        refreshAllButton.addActionListener(e -> refreshData());
        exportSoldButton.addActionListener(e -> exportSoldPlayers());
        viewSoldStatsButton.addActionListener(e -> viewSoldPlayersStatistics());
        exportUnsoldButton.addActionListener(e -> exportUnsoldPlayers());
        viewTeamDetailsButton.addActionListener(e -> viewSelectedTeamDetails());
        compareTeamsButton.addActionListener(e -> compareTeams());
        refreshSummaryButton.addActionListener(e -> refreshSummary());
        exportSummaryButton.addActionListener(e -> exportSummary());
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Double-click on sold players table to view details
        soldPlayersTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    viewSelectedPlayerDetails(soldPlayersTable, soldPlayersModel);
                }
            }
        });
        
        // Double-click on unsold players table to view details
        unsoldPlayersTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    viewSelectedPlayerDetails(unsoldPlayersTable, unsoldPlayersModel);
                }
            }
        });
        
        // Double-click on team summary table to view team details
        teamSummaryTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    viewSelectedTeamDetails();
                }
            }
        });
    }
    
    /**
     * Refresh all data
     */
    public void refreshData() {
        refreshSoldPlayers();
        refreshUnsoldPlayers();
        refreshTeamSummary();
        refreshSummary();
    }
    
    /**
     * Refresh sold players data
     */
    private void refreshSoldPlayers() {
        soldPlayersModel.setRowCount(0);
        List<Player> soldPlayers = auctionService.getPlayerDAO().getSoldPlayers();
        
        for (Player player : soldPlayers) {
            Team team = auctionService.getTeamDAO().getTeamById(player.getTeamId());
            double profitLoss = player.getSoldPrice() - player.getBasePrice();
            
            Object[] row = {
                player.getName(),
                player.getCountry(),
                player.getRole(),
                player.getAge(),
                player.getBasePrice(),
                player.getSoldPrice(),
                team != null ? team.getTeamName() : "Unknown",
                String.format("%.2f", profitLoss)
            };
            soldPlayersModel.addRow(row);
        }
    }
    
    /**
     * Refresh unsold players data
     */
    private void refreshUnsoldPlayers() {
        unsoldPlayersModel.setRowCount(0);
        List<Player> unsoldPlayers = auctionService.getPlayerDAO().getUnsoldPlayers();
        
        for (Player player : unsoldPlayers) {
            Object[] row = {
                player.getName(),
                player.getCountry(),
                player.getRole(),
                player.getAge(),
                player.getBasePrice(),
                player.getCategory()
            };
            unsoldPlayersModel.addRow(row);
        }
    }
    
    /**
     * Refresh team summary data
     */
    private void refreshTeamSummary() {
        teamSummaryModel.setRowCount(0);
        List<Team> teams = auctionService.getTeamDAO().getAllTeams();
        
        for (Team team : teams) {
            List<Player> teamPlayers = auctionService.getPlayerDAO().getPlayersByTeam(team.getTeamId());
            
            double totalSpent = team.getTotalSpent();
            double avgPrice = teamPlayers.isEmpty() ? 0 : totalSpent / teamPlayers.size();
            
            String mostExpensive = "None";
            if (!teamPlayers.isEmpty()) {
                Player expensivePlayer = teamPlayers.stream()
                    .max((p1, p2) -> Double.compare(p1.getSoldPrice(), p2.getSoldPrice()))
                    .orElse(null);
                if (expensivePlayer != null) {
                    mostExpensive = expensivePlayer.getName() + " (₹" + expensivePlayer.getSoldPrice() + " Cr)";
                }
            }
            
            Object[] row = {
                team.getTeamName(),
                teamPlayers.size() + "/" + team.getMaxPlayers(),
                totalSpent,
                team.getRemainingBudget(),
                String.format("%.2f", avgPrice),
                mostExpensive
            };
            teamSummaryModel.addRow(row);
        }
    }
    
    /**
     * Refresh overall summary
     */
    private void refreshSummary() {
        List<Player> soldPlayers = auctionService.getPlayerDAO().getSoldPlayers();
        List<Player> unsoldPlayers = auctionService.getPlayerDAO().getUnsoldPlayers();
        List<Team> teams = auctionService.getTeamDAO().getAllTeams();
        
        StringBuilder summary = new StringBuilder();
        summary.append("=== IPL AUCTION RESULTS SUMMARY ===\n\n");
        
        // Overall statistics
        summary.append("OVERALL STATISTICS:\n");
        summary.append("Total Players: ").append(soldPlayers.size() + unsoldPlayers.size()).append("\n");
        summary.append("Players Sold: ").append(soldPlayers.size()).append("\n");
        summary.append("Players Unsold: ").append(unsoldPlayers.size()).append("\n");
        
        if (!soldPlayers.isEmpty()) {
            double totalSpent = soldPlayers.stream().mapToDouble(Player::getSoldPrice).sum();
            double avgPrice = totalSpent / soldPlayers.size();
            
            summary.append("Total Amount Spent: ₹").append(String.format("%.2f", totalSpent)).append(" Cr\n");
            summary.append("Average Player Price: ₹").append(String.format("%.2f", avgPrice)).append(" Cr\n");
            
            // Most expensive player
            Player mostExpensive = soldPlayers.stream()
                .max((p1, p2) -> Double.compare(p1.getSoldPrice(), p2.getSoldPrice()))
                .orElse(null);
            if (mostExpensive != null) {
                Team team = auctionService.getTeamDAO().getTeamById(mostExpensive.getTeamId());
                summary.append("Most Expensive: ").append(mostExpensive.getName())
                       .append(" - ₹").append(mostExpensive.getSoldPrice()).append(" Cr")
                       .append(" (").append(team != null ? team.getTeamName() : "Unknown").append(")\n");
            }
            
            // Least expensive player
            Player leastExpensive = soldPlayers.stream()
                .min((p1, p2) -> Double.compare(p1.getSoldPrice(), p2.getSoldPrice()))
                .orElse(null);
            if (leastExpensive != null) {
                Team team = auctionService.getTeamDAO().getTeamById(leastExpensive.getTeamId());
                summary.append("Least Expensive: ").append(leastExpensive.getName())
                       .append(" - ₹").append(leastExpensive.getSoldPrice()).append(" Cr")
                       .append(" (").append(team != null ? team.getTeamName() : "Unknown").append(")\n");
            }
        }
        
        summary.append("\n");
        
        // Team-wise breakdown
        summary.append("TEAM-WISE BREAKDOWN:\n");
        for (Team team : teams) {
            List<Player> teamPlayers = auctionService.getPlayerDAO().getPlayersByTeam(team.getTeamId());
            summary.append("\n").append(team.getTeamName()).append(":\n");
            summary.append("  Players: ").append(teamPlayers.size()).append("/").append(team.getMaxPlayers()).append("\n");
            summary.append("  Spent: ₹").append(String.format("%.2f", team.getTotalSpent())).append(" Cr\n");
            summary.append("  Remaining: ₹").append(String.format("%.2f", team.getRemainingBudget())).append(" Cr\n");
            
            // Role-wise count
            long batsmen = teamPlayers.stream().filter(p -> "Batsman".equals(p.getRole())).count();
            long bowlers = teamPlayers.stream().filter(p -> "Bowler".equals(p.getRole())).count();
            long allRounders = teamPlayers.stream().filter(p -> "All-rounder".equals(p.getRole())).count();
            long wicketKeepers = teamPlayers.stream().filter(p -> "Wicket-keeper".equals(p.getRole())).count();
            
            summary.append("  Composition: ").append(batsmen).append(" Batsmen, ")
                   .append(bowlers).append(" Bowlers, ").append(allRounders).append(" All-rounders, ")
                   .append(wicketKeepers).append(" Wicket-keepers\n");
        }
        
        summaryTextArea.setText(summary.toString());
    }
    
    /**
     * View selected player details
     */
    private void viewSelectedPlayerDetails(JTable table, DefaultTableModel model) {
        int selectedRow = table.getSelectedRow();
        if (selectedRow == -1) return;
        
        String playerName = (String) model.getValueAt(selectedRow, 0);
        List<Player> players = table == soldPlayersTable ? 
            auctionService.getPlayerDAO().getSoldPlayers() : 
            auctionService.getPlayerDAO().getUnsoldPlayers();
        
        Player player = players.stream()
            .filter(p -> p.getName().equals(playerName))
            .findFirst().orElse(null);
        
        if (player != null) {
            JDialog dialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(this), 
                "Player Details - " + playerName, true);
            dialog.setSize(400, 300);
            dialog.setLocationRelativeTo(this);
            
            JTextArea textArea = new JTextArea(player.getDetailedInfo());
            textArea.setEditable(false);
            textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            dialog.add(new JScrollPane(textArea));
            dialog.setVisible(true);
        }
    }
    
    /**
     * View selected team details
     */
    private void viewSelectedTeamDetails() {
        int selectedRow = teamSummaryTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a team");
            return;
        }
        
        String teamName = (String) teamSummaryModel.getValueAt(selectedRow, 0);
        Team team = auctionService.getTeamDAO().getTeamByName(teamName);
        
        if (team != null) {
            String teamDetails = auctionService.getTeamDAO().getTeamStatistics(team.getTeamId());
            
            JDialog dialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(this), 
                teamName + " - Details", true);
            dialog.setSize(500, 400);
            dialog.setLocationRelativeTo(this);
            
            JTextArea textArea = new JTextArea(teamDetails);
            textArea.setEditable(false);
            textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            dialog.add(new JScrollPane(textArea));
            dialog.setVisible(true);
        }
    }
    
    /**
     * Export sold players (placeholder)
     */
    private void exportSoldPlayers() {
        JOptionPane.showMessageDialog(this, "Export functionality would be implemented here");
    }
    
    /**
     * View sold players statistics
     */
    private void viewSoldPlayersStatistics() {
        List<Player> soldPlayers = auctionService.getPlayerDAO().getSoldPlayers();
        
        if (soldPlayers.isEmpty()) {
            JOptionPane.showMessageDialog(this, "No sold players to analyze");
            return;
        }
        
        StringBuilder stats = new StringBuilder();
        stats.append("=== SOLD PLAYERS STATISTICS ===\n\n");
        
        // Role-wise breakdown
        long batsmen = soldPlayers.stream().filter(p -> "Batsman".equals(p.getRole())).count();
        long bowlers = soldPlayers.stream().filter(p -> "Bowler".equals(p.getRole())).count();
        long allRounders = soldPlayers.stream().filter(p -> "All-rounder".equals(p.getRole())).count();
        long wicketKeepers = soldPlayers.stream().filter(p -> "Wicket-keeper".equals(p.getRole())).count();
        
        stats.append("Role-wise Distribution:\n");
        stats.append("Batsmen: ").append(batsmen).append("\n");
        stats.append("Bowlers: ").append(bowlers).append("\n");
        stats.append("All-rounders: ").append(allRounders).append("\n");
        stats.append("Wicket-keepers: ").append(wicketKeepers).append("\n\n");
        
        // Price analysis
        double totalSpent = soldPlayers.stream().mapToDouble(Player::getSoldPrice).sum();
        double avgPrice = totalSpent / soldPlayers.size();
        
        stats.append("Price Analysis:\n");
        stats.append("Total Spent: ₹").append(String.format("%.2f", totalSpent)).append(" Cr\n");
        stats.append("Average Price: ₹").append(String.format("%.2f", avgPrice)).append(" Cr\n");
        
        JDialog dialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(this), 
            "Sold Players Statistics", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(this);
        
        JTextArea textArea = new JTextArea(stats.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        dialog.add(new JScrollPane(textArea));
        dialog.setVisible(true);
    }
    
    /**
     * Export unsold players (placeholder)
     */
    private void exportUnsoldPlayers() {
        JOptionPane.showMessageDialog(this, "Export functionality would be implemented here");
    }
    
    /**
     * Compare teams (placeholder)
     */
    private void compareTeams() {
        JOptionPane.showMessageDialog(this, "Team comparison functionality would be implemented here");
    }
    
    /**
     * Export summary (placeholder)
     */
    private void exportSummary() {
        JOptionPane.showMessageDialog(this, "Export functionality would be implemented here");
    }
}
