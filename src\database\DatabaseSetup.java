package database;

import config.DatabaseConfig;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Database setup class to create tables and initial data
 */
public class DatabaseSetup {
    private static final Logger LOGGER = Logger.getLogger(DatabaseSetup.class.getName());
    private DatabaseConnection dbConnection;
    
    public DatabaseSetup() {
        this.dbConnection = DatabaseConnection.getInstance();
    }
    
    /**
     * Initialize the database with all required tables
     */
    public boolean initializeDatabase() {
        try {
            createTables();
            insertDefaultData();
            LOGGER.info("Database initialized successfully");
            return true;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to initialize database", e);
            return false;
        }
    }
    
    /**
     * Create all required tables
     */
    private void createTables() throws SQLException {
        createTeamsTable();
        createPlayersTable();
        createAuctionsTable();
        createBidsTable();
        LOGGER.info("All tables created successfully");
    }
    
    /**
     * Create teams table
     */
    private void createTeamsTable() throws SQLException {
        String createTeamsTable = """
            CREATE TABLE IF NOT EXISTS teams (
                team_id INT AUTO_INCREMENT PRIMARY KEY,
                team_name VARCHAR(100) NOT NULL UNIQUE,
                city VARCHAR(50) NOT NULL,
                owner VARCHAR(100),
                budget DECIMAL(10,2) NOT NULL DEFAULT 100.00,
                remaining_budget DECIMAL(10,2) NOT NULL DEFAULT 100.00,
                max_players INT NOT NULL DEFAULT 25,
                current_player_count INT NOT NULL DEFAULT 0,
                coach VARCHAR(100),
                captain VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
            """;
        
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createTeamsTable);
            LOGGER.info("Teams table created successfully");
        }
    }
    
    /**
     * Create players table
     */
    private void createPlayersTable() throws SQLException {
        String createPlayersTable = """
            CREATE TABLE IF NOT EXISTS players (
                player_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                country VARCHAR(50) NOT NULL,
                role ENUM('Batsman', 'Bowler', 'All-rounder', 'Wicket-keeper') NOT NULL,
                age INT NOT NULL,
                base_price DECIMAL(10,2) NOT NULL,
                category ENUM('Capped', 'Uncapped') NOT NULL,
                is_sold BOOLEAN DEFAULT FALSE,
                team_id INT,
                sold_price DECIMAL(10,2) DEFAULT 0.00,
                special_skills TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (team_id) REFERENCES teams(team_id) ON DELETE SET NULL,
                INDEX idx_player_role (role),
                INDEX idx_player_country (country),
                INDEX idx_player_sold (is_sold)
            )
            """;
        
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createPlayersTable);
            LOGGER.info("Players table created successfully");
        }
    }
    
    /**
     * Create auctions table
     */
    private void createAuctionsTable() throws SQLException {
        String createAuctionsTable = """
            CREATE TABLE IF NOT EXISTS auctions (
                auction_id INT AUTO_INCREMENT PRIMARY KEY,
                auction_name VARCHAR(100) NOT NULL,
                start_time DATETIME,
                end_time DATETIME,
                status ENUM('SCHEDULED', 'ACTIVE', 'PAUSED', 'COMPLETED') DEFAULT 'SCHEDULED',
                current_player_id INT,
                current_bid DECIMAL(10,2) DEFAULT 0.00,
                current_bidding_team_id INT,
                auction_type ENUM('PLAYER', 'RETENTION') DEFAULT 'PLAYER',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (current_player_id) REFERENCES players(player_id) ON DELETE SET NULL,
                FOREIGN KEY (current_bidding_team_id) REFERENCES teams(team_id) ON DELETE SET NULL,
                INDEX idx_auction_status (status),
                INDEX idx_auction_type (auction_type)
            )
            """;
        
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createAuctionsTable);
            LOGGER.info("Auctions table created successfully");
        }
    }
    
    /**
     * Create bids table
     */
    private void createBidsTable() throws SQLException {
        String createBidsTable = """
            CREATE TABLE IF NOT EXISTS bids (
                bid_id INT AUTO_INCREMENT PRIMARY KEY,
                auction_id INT NOT NULL,
                player_id INT NOT NULL,
                team_id INT NOT NULL,
                bid_amount DECIMAL(10,2) NOT NULL,
                bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                bid_status ENUM('ACTIVE', 'OUTBID', 'WINNING', 'WITHDRAWN') DEFAULT 'ACTIVE',
                bid_type ENUM('INITIAL', 'INCREMENT', 'FINAL') DEFAULT 'INCREMENT',
                FOREIGN KEY (auction_id) REFERENCES auctions(auction_id) ON DELETE CASCADE,
                FOREIGN KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE,
                FOREIGN KEY (team_id) REFERENCES teams(team_id) ON DELETE CASCADE,
                INDEX idx_bid_auction (auction_id),
                INDEX idx_bid_player (player_id),
                INDEX idx_bid_team (team_id),
                INDEX idx_bid_status (bid_status)
            )
            """;
        
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createBidsTable);
            LOGGER.info("Bids table created successfully");
        }
    }
    
    /**
     * Insert default data
     */
    private void insertDefaultData() throws SQLException {
        insertDefaultTeams();
        insertSamplePlayers();
        LOGGER.info("Default data inserted successfully");
    }
    
    /**
     * Insert default IPL teams
     */
    private void insertDefaultTeams() throws SQLException {
        String insertTeamQuery = """
            INSERT IGNORE INTO teams (team_name, city, owner, budget, remaining_budget) 
            VALUES (?, ?, ?, ?, ?)
            """;
        
        try (Connection conn = dbConnection.getConnection()) {
            for (String[] teamData : DatabaseConfig.DEFAULT_TEAMS) {
                dbConnection.executePreparedUpdate(insertTeamQuery, 
                    teamData[0], teamData[1], teamData[2], 
                    DatabaseConfig.DEFAULT_TEAM_BUDGET, DatabaseConfig.DEFAULT_TEAM_BUDGET);
            }
            LOGGER.info("Default teams inserted successfully");
        }
    }
    
    /**
     * Insert sample players for demonstration
     */
    private void insertSamplePlayers() throws SQLException {
        String insertPlayerQuery = """
            INSERT IGNORE INTO players (name, country, role, age, base_price, category, special_skills) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """;
        
        // Sample players data
        Object[][] samplePlayers = {
            {"Virat Kohli", "India", "Batsman", 35, 2.0, "Capped", "Aggressive batting, Chase master"},
            {"Rohit Sharma", "India", "Batsman", 37, 2.0, "Capped", "Opening batsman, Captain"},
            {"Jasprit Bumrah", "India", "Bowler", 30, 2.0, "Capped", "Death bowling specialist"},
            {"Hardik Pandya", "India", "All-rounder", 30, 2.0, "Capped", "Power hitting, Medium pace"},
            {"MS Dhoni", "India", "Wicket-keeper", 42, 2.0, "Capped", "Finishing, Leadership"},
            {"David Warner", "Australia", "Batsman", 37, 2.0, "Capped", "Aggressive opener"},
            {"Pat Cummins", "Australia", "Bowler", 31, 2.0, "Capped", "Fast bowling, Leadership"},
            {"Jos Buttler", "England", "Wicket-keeper", 34, 2.0, "Capped", "Power hitting, Keeping"},
            {"Kagiso Rabada", "South Africa", "Bowler", 29, 2.0, "Capped", "Express pace bowling"},
            {"Rashid Khan", "Afghanistan", "Bowler", 26, 2.0, "Capped", "Leg spin bowling"}
        };
        
        try (Connection conn = dbConnection.getConnection()) {
            for (Object[] playerData : samplePlayers) {
                dbConnection.executePreparedUpdate(insertPlayerQuery, playerData);
            }
            LOGGER.info("Sample players inserted successfully");
        }
    }
    
    /**
     * Drop all tables (for testing purposes)
     */
    public void dropAllTables() throws SQLException {
        String[] dropQueries = {
            "DROP TABLE IF EXISTS bids",
            "DROP TABLE IF EXISTS auctions", 
            "DROP TABLE IF EXISTS players",
            "DROP TABLE IF EXISTS teams"
        };
        
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            for (String query : dropQueries) {
                stmt.executeUpdate(query);
            }
            LOGGER.info("All tables dropped successfully");
        }
    }
    
    /**
     * Check if database is properly initialized
     */
    public boolean isDatabaseInitialized() {
        try (Connection conn = dbConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // Check if all required tables exist
            String[] tables = {"teams", "players", "auctions", "bids"};
            for (String table : tables) {
                stmt.executeQuery("SELECT 1 FROM " + table + " LIMIT 1");
            }
            return true;
        } catch (SQLException e) {
            return false;
        }
    }
}
