#!/bin/bash

echo "IPL Auction System - Build and Run Script"
echo "=========================================="

# Create bin directory if it doesn't exist
mkdir -p bin

echo ""
echo "Compiling Java files..."
javac -cp "lib/*:src" -d bin src/IPLAuctionApp.java src/models/*.java src/config/*.java src/database/*.java src/dao/*.java src/services/*.java src/gui/*.java

if [ $? -ne 0 ]; then
    echo ""
    echo "Compilation failed! Please check for errors."
    exit 1
fi

echo "Compilation successful!"
echo ""
echo "Starting IPL Auction System..."
echo ""

# Run the application
java -cp "lib/*:bin" IPLAuctionApp

echo ""
echo "Application closed."
