package gui;

import models.Team;
import models.Player;
import services.AuctionService;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;

/**
 * Panel for managing teams in the IPL auction system
 */
public class TeamManagementPanel extends JPanel {
    private AuctionService auctionService;
    private JTable teamTable;
    private DefaultTableModel tableModel;
    private JTable playerTable;
    private DefaultTableModel playerTableModel;
    
    public TeamManagementPanel(AuctionService auctionService) {
        this.auctionService = auctionService;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        refreshData();
    }
    
    /**
     * Initialize GUI components
     */
    private void initializeComponents() {
        // Create team table model
        String[] teamColumns = {"ID", "Team Name", "City", "Owner", "Budget (Cr)", 
                               "Remaining (Cr)", "Players", "Total Spent (Cr)"};
        tableModel = new DefaultTableModel(teamColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        // Create team table
        teamTable = new JTable(tableModel);
        teamTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        teamTable.setRowHeight(25);
        teamTable.getTableHeader().setReorderingAllowed(false);
        
        // Create player table model for team squad
        String[] playerColumns = {"Name", "Role", "Country", "Age", "Price (Cr)"};
        playerTableModel = new DefaultTableModel(playerColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        // Create player table
        playerTable = new JTable(playerTableModel);
        playerTable.setRowHeight(25);
        playerTable.getTableHeader().setReorderingAllowed(false);
    }
    
    /**
     * Setup panel layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create split pane
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.6);
        
        // Top panel - Teams
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createTitledBorder("Teams"));
        
        JScrollPane teamScrollPane = new JScrollPane(teamTable);
        topPanel.add(teamScrollPane, BorderLayout.CENTER);
        
        // Team buttons
        JPanel teamButtonPanel = new JPanel(new FlowLayout());
        JButton addTeamButton = new JButton("Add Team");
        JButton editTeamButton = new JButton("Edit Team");
        JButton deleteTeamButton = new JButton("Delete Team");
        JButton viewStatsButton = new JButton("View Statistics");
        JButton refreshTeamsButton = new JButton("Refresh");
        
        teamButtonPanel.add(addTeamButton);
        teamButtonPanel.add(editTeamButton);
        teamButtonPanel.add(deleteTeamButton);
        teamButtonPanel.add(viewStatsButton);
        teamButtonPanel.add(refreshTeamsButton);
        
        topPanel.add(teamButtonPanel, BorderLayout.SOUTH);
        
        // Bottom panel - Team Squad
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.setBorder(BorderFactory.createTitledBorder("Team Squad"));
        
        JScrollPane playerScrollPane = new JScrollPane(playerTable);
        bottomPanel.add(playerScrollPane, BorderLayout.CENTER);
        
        // Squad info panel
        JPanel squadInfoPanel = new JPanel(new FlowLayout());
        JLabel squadInfoLabel = new JLabel("Select a team to view squad");
        squadInfoPanel.add(squadInfoLabel);
        bottomPanel.add(squadInfoPanel, BorderLayout.SOUTH);
        
        splitPane.setTopComponent(topPanel);
        splitPane.setBottomComponent(bottomPanel);
        
        add(splitPane, BorderLayout.CENTER);
        
        // Setup button actions
        addTeamButton.addActionListener(e -> showAddTeamDialog());
        editTeamButton.addActionListener(e -> showEditTeamDialog());
        deleteTeamButton.addActionListener(e -> deleteSelectedTeam());
        viewStatsButton.addActionListener(e -> showTeamStatistics());
        refreshTeamsButton.addActionListener(e -> refreshData());
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Team selection change
        teamTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                updateTeamSquad();
            }
        });
        
        // Double-click to edit team
        teamTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    showEditTeamDialog();
                }
            }
        });
    }
    
    /**
     * Refresh team data
     */
    public void refreshData() {
        List<Team> teams = auctionService.getTeamDAO().getAllTeams();
        updateTeamTable(teams);
        updateTeamSquad(); // Clear squad display
    }
    
    /**
     * Update team table
     */
    private void updateTeamTable(List<Team> teams) {
        tableModel.setRowCount(0);
        
        for (Team team : teams) {
            Object[] row = {
                team.getTeamId(),
                team.getTeamName(),
                team.getCity(),
                team.getOwner(),
                team.getBudget(),
                team.getRemainingBudget(),
                team.getCurrentPlayerCount() + "/" + team.getMaxPlayers(),
                team.getTotalSpent()
            };
            tableModel.addRow(row);
        }
    }
    
    /**
     * Update team squad display
     */
    private void updateTeamSquad() {
        playerTableModel.setRowCount(0);
        
        int selectedRow = teamTable.getSelectedRow();
        if (selectedRow == -1) {
            return;
        }
        
        int teamId = (Integer) tableModel.getValueAt(selectedRow, 0);
        List<Player> players = auctionService.getPlayerDAO().getPlayersByTeam(teamId);
        
        for (Player player : players) {
            Object[] row = {
                player.getName(),
                player.getRole(),
                player.getCountry(),
                player.getAge(),
                player.getSoldPrice()
            };
            playerTableModel.addRow(row);
        }
        
        // Update squad info
        String teamName = (String) tableModel.getValueAt(selectedRow, 1);
        JPanel squadInfoPanel = (JPanel) ((JPanel) ((JSplitPane) getComponent(0))
            .getBottomComponent()).getComponent(1);
        JLabel squadInfoLabel = (JLabel) squadInfoPanel.getComponent(0);
        squadInfoLabel.setText(teamName + " Squad - " + players.size() + " players");
    }
    
    /**
     * Show add team dialog
     */
    private void showAddTeamDialog() {
        TeamDialog dialog = new TeamDialog(
            (JFrame) SwingUtilities.getWindowAncestor(this), 
            "Add New Team", null, auctionService);
        dialog.setVisible(true);
        
        if (dialog.isConfirmed()) {
            refreshData();
        }
    }
    
    /**
     * Show edit team dialog
     */
    private void showEditTeamDialog() {
        int selectedRow = teamTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a team to edit");
            return;
        }
        
        int teamId = (Integer) tableModel.getValueAt(selectedRow, 0);
        Team team = auctionService.getTeamDAO().getTeamById(teamId);
        
        if (team != null) {
            TeamDialog dialog = new TeamDialog(
                (JFrame) SwingUtilities.getWindowAncestor(this), 
                "Edit Team", team, auctionService);
            dialog.setVisible(true);
            
            if (dialog.isConfirmed()) {
                refreshData();
            }
        }
    }
    
    /**
     * Delete selected team
     */
    private void deleteSelectedTeam() {
        int selectedRow = teamTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a team to delete");
            return;
        }
        
        int teamId = (Integer) tableModel.getValueAt(selectedRow, 0);
        String teamName = (String) tableModel.getValueAt(selectedRow, 1);
        
        // Check if team has players
        List<Player> teamPlayers = auctionService.getPlayerDAO().getPlayersByTeam(teamId);
        if (!teamPlayers.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "Cannot delete team with players. Please release players first.",
                "Cannot Delete", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        int result = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to delete team: " + teamName + "?",
            "Confirm Delete", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (auctionService.getTeamDAO().deleteTeam(teamId)) {
                JOptionPane.showMessageDialog(this, "Team deleted successfully");
                refreshData();
            } else {
                JOptionPane.showMessageDialog(this, "Failed to delete team", 
                    "Error", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * Show team statistics
     */
    private void showTeamStatistics() {
        int selectedRow = teamTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Please select a team to view statistics");
            return;
        }
        
        int teamId = (Integer) tableModel.getValueAt(selectedRow, 0);
        String teamName = (String) tableModel.getValueAt(selectedRow, 1);
        
        String statistics = auctionService.getTeamDAO().getTeamStatistics(teamId);
        
        JDialog dialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(this), 
            teamName + " - Statistics", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(this);
        
        JTextArea textArea = new JTextArea(statistics);
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        dialog.add(new JScrollPane(textArea));
        dialog.setVisible(true);
    }
}
