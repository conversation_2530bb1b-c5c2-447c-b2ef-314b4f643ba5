# IPL Auction System

A comprehensive IPL (Indian Premier League) auction management system built with Java Swing and MySQL. This application provides a complete solution for managing cricket player auctions with a professional graphical user interface.

## Features

### 🏏 Player Management
- Add, edit, and delete player profiles
- Search and filter players by role, country, and status
- Detailed player information including skills and statistics
- Support for both capped and uncapped players

### 🏆 Team Management
- Manage IPL team information and budgets
- Track team composition and spending
- View team statistics and squad details
- Budget management with real-time updates

### 🎯 Live Auction
- Real-time auction interface
- Place bids with validation
- Track current bids and bidding history
- Auction control (start, pause, resume, complete)
- Automatic player progression

### 📊 Results & Analytics
- Comprehensive auction results
- Team-wise analysis and comparisons
- Player statistics and trends
- Export functionality for reports

## Technology Stack

- **Frontend**: Java Swing with custom UI components
- **Backend**: Java with JDBC
- **Database**: MySQL 8.0+
- **Architecture**: MVC pattern with DAO layer

## Prerequisites

- Java 11 or higher
- MySQL 8.0 or higher
- MySQL JDBC Driver (included in lib folder)
- 4GB RAM (recommended)
- 1920x1080 screen resolution (recommended)

## Installation & Setup

### 1. Database Setup

1. Install and start MySQL server
2. Create a new database user (optional) or use root
3. Run the database setup script:
   ```sql
   mysql -u root -p < database_schema.sql
   ```

### 2. Database Configuration

1. Open `src/config/DatabaseConfig.java`
2. Update the database connection parameters:
   ```java
   public static final String DB_URL = "***************************************";
   public static final String DB_USERNAME = "your_username";
   public static final String DB_PASSWORD = "your_password";
   ```

### 3. Add MySQL JDBC Driver

1. Download MySQL Connector/J from [MySQL website](https://dev.mysql.com/downloads/connector/j/)
2. Place the JAR file in the `lib` folder
3. Add to your IDE's classpath

### 4. Compile and Run

#### Using Command Line:
```bash
# Compile
javac -cp "lib/*:src" src/IPLAuctionApp.java -d bin

# Run
java -cp "lib/*:bin" IPLAuctionApp
```

#### Using IDE:
1. Import the project into your IDE
2. Add MySQL JDBC driver to classpath
3. Run `IPLAuctionApp.java`

## Usage Guide

### First Time Setup
1. Launch the application
2. The system will prompt to initialize the database
3. Click "Yes" to create tables and insert sample data
4. The application will start with default teams and sample players

### Managing Players
1. Go to the "Players" tab
2. Use "Add Player" to register new players
3. Search and filter players using the top panel
4. Double-click any player to view detailed information

### Managing Teams
1. Go to the "Teams" tab
2. View all registered teams and their current status
3. Select a team to see their current squad
4. Use "Add Team" to register new teams

### Conducting Auctions
1. Go to the "Auction" tab
2. Create a new auction using File → New Auction
3. Start the auction using the "Start Auction" button
4. Use "Next Player" to bring players up for auction
5. Teams can place bids using the bidding panel
6. Mark players as "SOLD" or "UNSOLD"

### Viewing Results
1. Go to the "Results" tab
2. View sold/unsold players, team summaries, and overall statistics
3. Export data for external analysis

## Project Structure

```
src/
├── IPLAuctionApp.java          # Main application entry point
├── config/
│   └── DatabaseConfig.java     # Database configuration
├── database/
│   ├── DatabaseConnection.java # Connection management
│   └── DatabaseSetup.java     # Database initialization
├── models/
│   ├── Player.java            # Player entity
│   ├── Team.java              # Team entity
│   ├── Auction.java           # Auction entity
│   └── Bid.java               # Bid entity
├── dao/
│   ├── PlayerDAO.java         # Player data access
│   ├── TeamDAO.java           # Team data access
│   └── AuctionDAO.java        # Auction data access
├── services/
│   └── AuctionService.java    # Business logic
└── gui/
    ├── MainFrame.java         # Main application window
    ├── PlayerManagementPanel.java
    ├── TeamManagementPanel.java
    ├── AuctionPanel.java
    ├── ResultsPanel.java
    ├── PlayerDialog.java
    └── TeamDialog.java
```

## Database Schema

The application uses four main tables:
- `teams` - Team information and budgets
- `players` - Player profiles and auction status
- `auctions` - Auction sessions and current state
- `bids` - Bidding history and transactions

## Configuration

### Database Settings
- Default budget per team: ₹100 Crores
- Maximum players per team: 25
- Minimum bid increment: ₹0.25 Crores

### Customization
- Modify `DatabaseConfig.java` for different settings
- Update team information in the database
- Add custom player categories or roles

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL server is running
   - Verify connection parameters in DatabaseConfig.java
   - Ensure MySQL JDBC driver is in classpath

2. **Application Won't Start**
   - Check Java version (requires Java 11+)
   - Verify all dependencies are available
   - Check console for error messages

3. **GUI Issues**
   - Try different look and feel settings
   - Check screen resolution compatibility
   - Update graphics drivers

### Getting Help
- Check the application logs for detailed error messages
- Verify database connectivity using MySQL command line
- Ensure all required JAR files are present

## Contributing

This is a complete auction management system that can be extended with additional features:
- Advanced analytics and reporting
- Multi-language support
- Web-based interface
- Real-time notifications
- Integration with external APIs

## License

This project is developed for educational and demonstration purposes.

## Version History

- **v1.0** - Initial release with core auction functionality
  - Player and team management
  - Live auction interface
  - Results and analytics
  - MySQL database integration
