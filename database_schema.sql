-- IPL Auction System Database Schema
-- MySQL Database Setup Script

-- Use existing database
USE dbm;

-- Create teams table
CREATE TABLE IF NOT EXISTS teams (
    team_id INT AUTO_INCREMENT PRIMARY KEY,
    team_name VARCHA<PERSON>(100) NOT NULL UNIQUE,
    city VARCHAR(50) NOT NULL,
    owner <PERSON><PERSON><PERSON><PERSON>(100),
    budget DECIMAL(10,2) NOT NULL DEFAULT 100.00,
    remaining_budget DECIMAL(10,2) NOT NULL DEFAULT 100.00,
    max_players INT NOT NULL DEFAULT 25,
    current_player_count INT NOT NULL DEFAULT 0,
    coach <PERSON><PERSON><PERSON><PERSON>(100),
    captain <PERSON><PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_team_name (team_name),
    INDEX idx_city (city)
);

-- Create players table
CREATE TABLE IF NOT EXISTS players (
    player_id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    country VARCHAR(50) NOT NULL,
    role ENUM('Batsman', 'Bowler', 'All-rounder', 'Wicket-keeper') NOT NULL,
    age INT NOT NULL,
    base_price DECIMAL(10,2) NOT NULL,
    category ENUM('Capped', 'Uncapped') NOT NULL,
    is_sold BOOLEAN DEFAULT FALSE,
    team_id INT,
    sold_price DECIMAL(10,2) DEFAULT 0.00,
    special_skills TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (team_id) REFERENCES teams(team_id) ON DELETE SET NULL,
    INDEX idx_player_name (name),
    INDEX idx_player_role (role),
    INDEX idx_player_country (country),
    INDEX idx_player_sold (is_sold),
    INDEX idx_player_team (team_id)
);

-- Create auctions table
CREATE TABLE IF NOT EXISTS auctions (
    auction_id INT AUTO_INCREMENT PRIMARY KEY,
    auction_name VARCHAR(100) NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    status ENUM('SCHEDULED', 'ACTIVE', 'PAUSED', 'COMPLETED') DEFAULT 'SCHEDULED',
    current_player_id INT,
    current_bid DECIMAL(10,2) DEFAULT 0.00,
    current_bidding_team_id INT,
    auction_type ENUM('PLAYER', 'RETENTION') DEFAULT 'PLAYER',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (current_player_id) REFERENCES players(player_id) ON DELETE SET NULL,
    FOREIGN KEY (current_bidding_team_id) REFERENCES teams(team_id) ON DELETE SET NULL,
    INDEX idx_auction_status (status),
    INDEX idx_auction_type (auction_type),
    INDEX idx_auction_name (auction_name)
);

-- Create bids table
CREATE TABLE IF NOT EXISTS bids (
    bid_id INT AUTO_INCREMENT PRIMARY KEY,
    auction_id INT NOT NULL,
    player_id INT NOT NULL,
    team_id INT NOT NULL,
    bid_amount DECIMAL(10,2) NOT NULL,
    bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    bid_status ENUM('ACTIVE', 'OUTBID', 'WINNING', 'WITHDRAWN') DEFAULT 'ACTIVE',
    bid_type ENUM('INITIAL', 'INCREMENT', 'FINAL') DEFAULT 'INCREMENT',
    FOREIGN KEY (auction_id) REFERENCES auctions(auction_id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE,
    FOREIGN KEY (team_id) REFERENCES teams(team_id) ON DELETE CASCADE,
    INDEX idx_bid_auction (auction_id),
    INDEX idx_bid_player (player_id),
    INDEX idx_bid_team (team_id),
    INDEX idx_bid_status (bid_status),
    INDEX idx_bid_time (bid_time)
);

-- Insert default IPL teams
INSERT IGNORE INTO teams (team_name, city, owner, budget, remaining_budget) VALUES
('Mumbai Indians', 'Mumbai', 'Reliance Industries', 100.00, 100.00),
('Chennai Super Kings', 'Chennai', 'Chennai Super Kings Cricket Ltd', 100.00, 100.00),
('Royal Challengers Bangalore', 'Bangalore', 'United Spirits', 100.00, 100.00),
('Kolkata Knight Riders', 'Kolkata', 'Red Chillies Entertainment', 100.00, 100.00),
('Delhi Capitals', 'Delhi', 'JSW Group', 100.00, 100.00),
('Punjab Kings', 'Punjab', 'Mohit Burman', 100.00, 100.00),
('Rajasthan Royals', 'Rajasthan', 'Manoj Badale', 100.00, 100.00),
('Sunrisers Hyderabad', 'Hyderabad', 'Sun TV Network', 100.00, 100.00);

-- Insert sample players
INSERT IGNORE INTO players (name, country, role, age, base_price, category, special_skills) VALUES
-- Indian Players
('Virat Kohli', 'India', 'Batsman', 35, 2.0, 'Capped', 'Aggressive batting, Chase master, Former captain'),
('Rohit Sharma', 'India', 'Batsman', 37, 2.0, 'Capped', 'Opening batsman, Captain, Record holder'),
('Jasprit Bumrah', 'India', 'Bowler', 30, 2.0, 'Capped', 'Death bowling specialist, Yorker expert'),
('Hardik Pandya', 'India', 'All-rounder', 30, 2.0, 'Capped', 'Power hitting, Medium pace bowling'),
('MS Dhoni', 'India', 'Wicket-keeper', 42, 2.0, 'Capped', 'Finishing, Leadership, Wicket-keeping'),
('KL Rahul', 'India', 'Wicket-keeper', 32, 2.0, 'Capped', 'Opening batsman, Wicket-keeping'),
('Ravindra Jadeja', 'India', 'All-rounder', 35, 2.0, 'Capped', 'Left-arm spin, Fielding, Lower-order batting'),
('Shikhar Dhawan', 'India', 'Batsman', 38, 1.5, 'Capped', 'Opening batsman, Tournament specialist'),
('Bhuvneshwar Kumar', 'India', 'Bowler', 34, 1.5, 'Capped', 'Swing bowling, Death overs'),
('Yuzvendra Chahal', 'India', 'Bowler', 34, 1.5, 'Capped', 'Leg-spin bowling, Wicket-taking'),

-- International Players
('David Warner', 'Australia', 'Batsman', 37, 2.0, 'Capped', 'Aggressive opener, Left-handed'),
('Pat Cummins', 'Australia', 'Bowler', 31, 2.0, 'Capped', 'Fast bowling, Leadership, All-format player'),
('Steve Smith', 'Australia', 'Batsman', 35, 2.0, 'Capped', 'Middle-order batting, Former captain'),
('Jos Buttler', 'England', 'Wicket-keeper', 34, 2.0, 'Capped', 'Power hitting, Wicket-keeping, Captaincy'),
('Ben Stokes', 'England', 'All-rounder', 33, 2.0, 'Capped', 'All-round skills, Match-winner'),
('Jonny Bairstow', 'England', 'Wicket-keeper', 34, 1.5, 'Capped', 'Aggressive batting, Wicket-keeping'),
('Kagiso Rabada', 'South Africa', 'Bowler', 29, 2.0, 'Capped', 'Express pace bowling, Wicket-taking'),
('Quinton de Kock', 'South Africa', 'Wicket-keeper', 31, 1.5, 'Capped', 'Left-handed batting, Wicket-keeping'),
('Rashid Khan', 'Afghanistan', 'Bowler', 26, 2.0, 'Capped', 'Leg-spin bowling, Economy rate'),
('Trent Boult', 'New Zealand', 'Bowler', 35, 1.5, 'Capped', 'Left-arm pace, Swing bowling'),

-- Uncapped/Young Players
('Shubman Gill', 'India', 'Batsman', 25, 1.0, 'Capped', 'Young talent, Opening batsman'),
('Prithvi Shaw', 'India', 'Batsman', 24, 0.75, 'Capped', 'Aggressive opener, Young talent'),
('Ishan Kishan', 'India', 'Wicket-keeper', 26, 1.0, 'Capped', 'Left-handed batsman, Wicket-keeping'),
('Ruturaj Gaikwad', 'India', 'Batsman', 27, 1.0, 'Capped', 'Consistent opener, Young talent'),
('Arshdeep Singh', 'India', 'Bowler', 25, 0.75, 'Capped', 'Left-arm pace, Death bowling'),
('Umran Malik', 'India', 'Bowler', 24, 0.5, 'Uncapped', 'Express pace, Raw talent'),
('Tilak Varma', 'India', 'Batsman', 22, 0.5, 'Uncapped', 'Left-handed batsman, Young talent'),
('Abhishek Sharma', 'India', 'All-rounder', 24, 0.5, 'Uncapped', 'Left-handed batsman, Part-time bowling'),
('Mayank Yadav', 'India', 'Bowler', 22, 0.2, 'Uncapped', 'Fast bowler, Emerging talent'),
('Riyan Parag', 'India', 'All-rounder', 22, 0.5, 'Uncapped', 'Middle-order batsman, Off-spin bowling');

-- Create a sample auction
INSERT IGNORE INTO auctions (auction_name, auction_type, description) VALUES
('IPL 2024 Mega Auction', 'PLAYER', 'Annual IPL player auction for 2024 season');

-- Create some useful views
CREATE OR REPLACE VIEW player_summary AS
SELECT 
    p.player_id,
    p.name,
    p.country,
    p.role,
    p.age,
    p.base_price,
    p.category,
    p.is_sold,
    p.sold_price,
    t.team_name,
    CASE 
        WHEN p.is_sold THEN p.sold_price - p.base_price 
        ELSE 0 
    END as profit_loss
FROM players p
LEFT JOIN teams t ON p.team_id = t.team_id;

CREATE OR REPLACE VIEW team_summary AS
SELECT 
    t.team_id,
    t.team_name,
    t.city,
    t.budget,
    t.remaining_budget,
    t.budget - t.remaining_budget as total_spent,
    t.current_player_count,
    t.max_players,
    COUNT(p.player_id) as actual_player_count,
    COALESCE(AVG(p.sold_price), 0) as avg_player_price,
    COALESCE(MAX(p.sold_price), 0) as max_player_price
FROM teams t
LEFT JOIN players p ON t.team_id = p.team_id AND p.is_sold = TRUE
GROUP BY t.team_id, t.team_name, t.city, t.budget, t.remaining_budget, t.current_player_count, t.max_players;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetTeamSquad(IN team_id INT)
BEGIN
    SELECT 
        p.name,
        p.country,
        p.role,
        p.age,
        p.sold_price,
        p.special_skills
    FROM players p
    WHERE p.team_id = team_id AND p.is_sold = TRUE
    ORDER BY p.sold_price DESC;
END //

CREATE PROCEDURE GetAuctionSummary(IN auction_id INT)
BEGIN
    SELECT 
        COUNT(CASE WHEN p.is_sold THEN 1 END) as players_sold,
        COUNT(CASE WHEN NOT p.is_sold THEN 1 END) as players_unsold,
        COALESCE(SUM(CASE WHEN p.is_sold THEN p.sold_price END), 0) as total_spent,
        COALESCE(AVG(CASE WHEN p.is_sold THEN p.sold_price END), 0) as avg_price,
        COALESCE(MAX(CASE WHEN p.is_sold THEN p.sold_price END), 0) as max_price,
        COALESCE(MIN(CASE WHEN p.is_sold THEN p.sold_price END), 0) as min_price
    FROM players p;
END //

DELIMITER ;

-- Grant permissions (adjust username as needed)
-- GRANT ALL PRIVILEGES ON ipl_auction.* TO 'root'@'localhost';
-- FLUSH PRIVILEGES;

-- Display setup completion message
SELECT 'IPL Auction Database Setup Complete!' as Status;
