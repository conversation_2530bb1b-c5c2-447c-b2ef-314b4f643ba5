package dao;

import database.DatabaseConnection;
import models.Team;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Data Access Object for Team operations
 */
public class TeamDAO {
    private static final Logger LOGGER = Logger.getLogger(TeamDAO.class.getName());
    private DatabaseConnection dbConnection;
    
    public TeamDAO() {
        this.dbConnection = DatabaseConnection.getInstance();
    }
    
    /**
     * Add a new team to the database
     */
    public boolean addTeam(Team team) {
        String query = """
            INSERT INTO teams (team_name, city, owner, budget, remaining_budget, max_players, coach, captain) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        try {
            ResultSet rs = dbConnection.executePreparedUpdateWithKeys(query,
                team.getTeamName(), team.getCity(), team.getOwner(),
                team.getBudget(), team.getRemainingBudget(), team.getMaxPlayers(),
                team.getCoach(), team.getCaptain());
            
            if (rs.next()) {
                team.setTeamId(rs.getInt(1));
                LOGGER.info("Team added successfully: " + team.getTeamName());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error adding team: " + team.getTeamName(), e);
        }
        return false;
    }
    
    /**
     * Get team by ID
     */
    public Team getTeamById(int teamId) {
        String query = "SELECT * FROM teams WHERE team_id = ?";
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, teamId)) {
            if (rs.next()) {
                return mapResultSetToTeam(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting team by ID: " + teamId, e);
        }
        return null;
    }
    
    /**
     * Get team by name
     */
    public Team getTeamByName(String teamName) {
        String query = "SELECT * FROM teams WHERE team_name = ?";
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, teamName)) {
            if (rs.next()) {
                return mapResultSetToTeam(rs);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting team by name: " + teamName, e);
        }
        return null;
    }
    
    /**
     * Get all teams
     */
    public List<Team> getAllTeams() {
        String query = "SELECT * FROM teams ORDER BY team_name";
        List<Team> teams = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                teams.add(mapResultSetToTeam(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting all teams", e);
        }
        return teams;
    }
    
    /**
     * Update team information
     */
    public boolean updateTeam(Team team) {
        String query = """
            UPDATE teams SET team_name = ?, city = ?, owner = ?, budget = ?, 
            remaining_budget = ?, max_players = ?, current_player_count = ?, 
            coach = ?, captain = ? 
            WHERE team_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query,
                team.getTeamName(), team.getCity(), team.getOwner(),
                team.getBudget(), team.getRemainingBudget(), team.getMaxPlayers(),
                team.getCurrentPlayerCount(), team.getCoach(), team.getCaptain(),
                team.getTeamId());
            
            if (rowsAffected > 0) {
                LOGGER.info("Team updated successfully: " + team.getTeamName());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating team: " + team.getTeamName(), e);
        }
        return false;
    }
    
    /**
     * Update team budget after a purchase
     */
    public boolean updateTeamBudget(int teamId, double newRemainingBudget, int newPlayerCount) {
        String query = """
            UPDATE teams SET remaining_budget = ?, current_player_count = ? 
            WHERE team_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, 
                newRemainingBudget, newPlayerCount, teamId);
            
            if (rowsAffected > 0) {
                LOGGER.info("Team budget updated successfully for team ID: " + teamId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating team budget for ID: " + teamId, e);
        }
        return false;
    }
    
    /**
     * Reset team budget and player count (for new auction)
     */
    public boolean resetTeamForNewAuction(int teamId, double newBudget) {
        String query = """
            UPDATE teams SET budget = ?, remaining_budget = ?, current_player_count = 0 
            WHERE team_id = ?
            """;
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, 
                newBudget, newBudget, teamId);
            
            if (rowsAffected > 0) {
                LOGGER.info("Team reset for new auction: " + teamId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error resetting team for new auction: " + teamId, e);
        }
        return false;
    }
    
    /**
     * Delete team
     */
    public boolean deleteTeam(int teamId) {
        String query = "DELETE FROM teams WHERE team_id = ?";
        
        try {
            int rowsAffected = dbConnection.executePreparedUpdate(query, teamId);
            if (rowsAffected > 0) {
                LOGGER.info("Team deleted successfully: ID " + teamId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error deleting team: ID " + teamId, e);
        }
        return false;
    }
    
    /**
     * Get teams with available budget for a specific amount
     */
    public List<Team> getTeamsWithBudget(double minimumBudget) {
        String query = "SELECT * FROM teams WHERE remaining_budget >= ? ORDER BY remaining_budget DESC";
        List<Team> teams = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, minimumBudget)) {
            while (rs.next()) {
                teams.add(mapResultSetToTeam(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting teams with budget: " + minimumBudget, e);
        }
        return teams;
    }
    
    /**
     * Get teams with available player slots
     */
    public List<Team> getTeamsWithAvailableSlots() {
        String query = "SELECT * FROM teams WHERE current_player_count < max_players ORDER BY team_name";
        List<Team> teams = new ArrayList<>();
        
        try (ResultSet rs = dbConnection.executeQuery(query)) {
            while (rs.next()) {
                teams.add(mapResultSetToTeam(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting teams with available slots", e);
        }
        return teams;
    }
    
    /**
     * Get team statistics
     */
    public String getTeamStatistics(int teamId) {
        String query = """
            SELECT t.*, 
                   COUNT(p.player_id) as actual_player_count,
                   SUM(p.sold_price) as total_spent,
                   AVG(p.sold_price) as avg_player_price
            FROM teams t 
            LEFT JOIN players p ON t.team_id = p.team_id 
            WHERE t.team_id = ? 
            GROUP BY t.team_id
            """;
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, teamId)) {
            if (rs.next()) {
                StringBuilder stats = new StringBuilder();
                stats.append("Team: ").append(rs.getString("team_name")).append("\n");
                stats.append("Budget: ₹").append(rs.getDouble("budget")).append(" Cr\n");
                stats.append("Remaining: ₹").append(rs.getDouble("remaining_budget")).append(" Cr\n");
                stats.append("Players: ").append(rs.getInt("actual_player_count"))
                     .append("/").append(rs.getInt("max_players")).append("\n");
                stats.append("Total Spent: ₹").append(rs.getDouble("total_spent")).append(" Cr\n");
                stats.append("Avg Player Price: ₹").append(rs.getDouble("avg_player_price")).append(" Cr");
                return stats.toString();
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting team statistics: " + teamId, e);
        }
        return "Statistics not available";
    }
    
    /**
     * Check if team can afford a player
     */
    public boolean canTeamAffordPlayer(int teamId, double playerPrice) {
        String query = "SELECT remaining_budget, current_player_count, max_players FROM teams WHERE team_id = ?";
        
        try (ResultSet rs = dbConnection.executePreparedQuery(query, teamId)) {
            if (rs.next()) {
                double remainingBudget = rs.getDouble("remaining_budget");
                int currentPlayers = rs.getInt("current_player_count");
                int maxPlayers = rs.getInt("max_players");
                
                return remainingBudget >= playerPrice && currentPlayers < maxPlayers;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error checking if team can afford player: " + teamId, e);
        }
        return false;
    }
    
    /**
     * Map ResultSet to Team object
     */
    private Team mapResultSetToTeam(ResultSet rs) throws SQLException {
        return new Team(
            rs.getInt("team_id"),
            rs.getString("team_name"),
            rs.getString("city"),
            rs.getString("owner"),
            rs.getDouble("budget"),
            rs.getDouble("remaining_budget"),
            rs.getInt("current_player_count"),
            rs.getString("coach"),
            rs.getString("captain")
        );
    }
}
