import java.sql.*;

public class TestConnection {
    public static void main(String[] args) {
        String url = "*******************************";
        String username = "root";
        String password = "M@noj4188";
        
        System.out.println("Testing MySQL connection...");
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        System.out.println("Password: " + (password.isEmpty() ? "empty" : "***"));
        
        try {
            // Load MySQL driver
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✓ MySQL driver loaded successfully");
            
            // Test connection
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✓ Database connection successful!");
            
            // Test database
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT DATABASE() as current_db");
            if (rs.next()) {
                System.out.println("✓ Current database: " + rs.getString("current_db"));
            }
            
            // Check if tables exist
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables("dbm", null, "%", new String[]{"TABLE"});
            System.out.println("\nExisting tables in 'dbm' database:");
            boolean hasTable = false;
            while (tables.next()) {
                System.out.println("  - " + tables.getString("TABLE_NAME"));
                hasTable = true;
            }
            if (!hasTable) {
                System.out.println("  No tables found. You need to create the IPL auction tables.");
            }
            
            connection.close();
            System.out.println("\n✓ Connection test completed successfully!");
            
        } catch (ClassNotFoundException e) {
            System.err.println("✗ MySQL driver not found: " + e.getMessage());
            System.err.println("Make sure mysql-connector-j-8.2.0.jar is in the classpath");
        } catch (SQLException e) {
            System.err.println("✗ Database connection failed: " + e.getMessage());
            System.err.println("\nPossible solutions:");
            System.err.println("1. Check if MySQL server is running");
            System.err.println("2. Verify the database 'dbm' exists");
            System.err.println("3. Check username and password");
            System.err.println("4. Ensure MySQL is running on localhost:3306");
        }
    }
}
