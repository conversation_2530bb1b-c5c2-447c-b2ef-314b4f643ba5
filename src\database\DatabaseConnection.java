package database;

import config.DatabaseConfig;
import java.sql.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Database connection manager for IPL Auction application
 */
public class DatabaseConnection {
    private static final Logger LOGGER = Logger.getLogger(DatabaseConnection.class.getName());
    private static DatabaseConnection instance;
    private Connection connection;
    
    // Private constructor for singleton pattern
    private DatabaseConnection() {
        try {
            // Load MySQL JDBC driver
            Class.forName(DatabaseConfig.DB_DRIVER);
            LOGGER.info("MySQL JDBC Driver loaded successfully");
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "MySQL JDBC Driver not found", e);
            throw new RuntimeException("MySQL JDBC Driver not found", e);
        }
    }
    
    /**
     * Get singleton instance of DatabaseConnection
     */
    public static synchronized DatabaseConnection getInstance() {
        if (instance == null) {
            instance = new DatabaseConnection();
        }
        return instance;
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            try {
                // First try to connect to the database
                connection = DriverManager.getConnection(
                    DatabaseConfig.DB_URL, 
                    DatabaseConfig.DB_USERNAME, 
                    DatabaseConfig.DB_PASSWORD
                );
                LOGGER.info("Connected to IPL Auction database successfully");
            } catch (SQLException e) {
                // If database doesn't exist, create it
                LOGGER.info("Database doesn't exist, attempting to create it");
                createDatabase();
                connection = DriverManager.getConnection(
                    DatabaseConfig.DB_URL, 
                    DatabaseConfig.DB_USERNAME, 
                    DatabaseConfig.DB_PASSWORD
                );
                LOGGER.info("Connected to newly created IPL Auction database");
            }
        }
        return connection;
    }
    
    /**
     * Create the database if it doesn't exist (using existing dbm database)
     */
    private void createDatabase() throws SQLException {
        // Since we're using existing 'dbm' database, we don't need to create it
        // Just log that we're using the existing database
        LOGGER.info("Using existing 'dbm' database");
    }
    
    /**
     * Test database connection
     */
    public boolean testConnection() {
        try (Connection testConnection = getConnection()) {
            return testConnection != null && !testConnection.isClosed();
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Database connection test failed", e);
            return false;
        }
    }
    
    /**
     * Execute a query and return ResultSet
     */
    public ResultSet executeQuery(String query) throws SQLException {
        Connection conn = getConnection();
        Statement statement = conn.createStatement();
        return statement.executeQuery(query);
    }
    
    /**
     * Execute an update query (INSERT, UPDATE, DELETE)
     */
    public int executeUpdate(String query) throws SQLException {
        try (Connection conn = getConnection();
             Statement statement = conn.createStatement()) {
            return statement.executeUpdate(query);
        }
    }
    
    /**
     * Execute a prepared statement query
     */
    public ResultSet executePreparedQuery(String query, Object... parameters) throws SQLException {
        Connection conn = getConnection();
        PreparedStatement preparedStatement = conn.prepareStatement(query);
        
        // Set parameters
        for (int i = 0; i < parameters.length; i++) {
            preparedStatement.setObject(i + 1, parameters[i]);
        }
        
        return preparedStatement.executeQuery();
    }
    
    /**
     * Execute a prepared statement update
     */
    public int executePreparedUpdate(String query, Object... parameters) throws SQLException {
        try (Connection conn = getConnection();
             PreparedStatement preparedStatement = conn.prepareStatement(query)) {
            
            // Set parameters
            for (int i = 0; i < parameters.length; i++) {
                preparedStatement.setObject(i + 1, parameters[i]);
            }
            
            return preparedStatement.executeUpdate();
        }
    }
    
    /**
     * Execute a prepared statement and return generated keys
     */
    public ResultSet executePreparedUpdateWithKeys(String query, Object... parameters) throws SQLException {
        Connection conn = getConnection();
        PreparedStatement preparedStatement = conn.prepareStatement(query, Statement.RETURN_GENERATED_KEYS);
        
        // Set parameters
        for (int i = 0; i < parameters.length; i++) {
            preparedStatement.setObject(i + 1, parameters[i]);
        }
        
        preparedStatement.executeUpdate();
        return preparedStatement.getGeneratedKeys();
    }
    
    /**
     * Begin transaction
     */
    public void beginTransaction() throws SQLException {
        Connection conn = getConnection();
        conn.setAutoCommit(false);
    }
    
    /**
     * Commit transaction
     */
    public void commitTransaction() throws SQLException {
        Connection conn = getConnection();
        conn.commit();
        conn.setAutoCommit(true);
    }
    
    /**
     * Rollback transaction
     */
    public void rollbackTransaction() throws SQLException {
        Connection conn = getConnection();
        conn.rollback();
        conn.setAutoCommit(true);
    }
    
    /**
     * Close database connection
     */
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                LOGGER.info("Database connection closed");
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error closing database connection", e);
        }
    }
    
    /**
     * Close resources safely
     */
    public static void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error closing database resources", e);
        }
    }
}
