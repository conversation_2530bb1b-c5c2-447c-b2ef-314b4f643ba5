package models;

import java.util.ArrayList;
import java.util.List;

/**
 * Team model representing an IPL team
 */
public class Team {
    private int teamId;
    private String teamName;
    private String city;
    private String owner;
    private double budget;
    private double remainingBudget;
    private int maxPlayers;
    private int currentPlayerCount;
    private List<Player> players;
    private String coach;
    private String captain;
    
    // Constructors
    public Team() {
        this.players = new ArrayList<>();
        this.maxPlayers = 25; // IPL team limit
        this.currentPlayerCount = 0;
    }
    
    public Team(String teamName, String city, String owner, double budget) {
        this();
        this.teamName = teamName;
        this.city = city;
        this.owner = owner;
        this.budget = budget;
        this.remainingBudget = budget;
    }
    
    public Team(int teamId, String teamName, String city, String owner, 
                double budget, double remainingBudget, int currentPlayerCount,
                String coach, String captain) {
        this();
        this.teamId = teamId;
        this.teamName = teamName;
        this.city = city;
        this.owner = owner;
        this.budget = budget;
        this.remainingBudget = remainingBudget;
        this.currentPlayerCount = currentPlayerCount;
        this.coach = coach;
        this.captain = captain;
    }
    
    // Getters and Setters
    public int getTeamId() { return teamId; }
    public void setTeamId(int teamId) { this.teamId = teamId; }
    
    public String getTeamName() { return teamName; }
    public void setTeamName(String teamName) { this.teamName = teamName; }
    
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    
    public String getOwner() { return owner; }
    public void setOwner(String owner) { this.owner = owner; }
    
    public double getBudget() { return budget; }
    public void setBudget(double budget) { this.budget = budget; }
    
    public double getRemainingBudget() { return remainingBudget; }
    public void setRemainingBudget(double remainingBudget) { this.remainingBudget = remainingBudget; }
    
    public int getMaxPlayers() { return maxPlayers; }
    public void setMaxPlayers(int maxPlayers) { this.maxPlayers = maxPlayers; }
    
    public int getCurrentPlayerCount() { return currentPlayerCount; }
    public void setCurrentPlayerCount(int currentPlayerCount) { this.currentPlayerCount = currentPlayerCount; }
    
    public List<Player> getPlayers() { return players; }
    public void setPlayers(List<Player> players) { this.players = players; }
    
    public String getCoach() { return coach; }
    public void setCoach(String coach) { this.coach = coach; }
    
    public String getCaptain() { return captain; }
    public void setCaptain(String captain) { this.captain = captain; }
    
    // Business methods
    public boolean canBuyPlayer(double playerPrice) {
        return remainingBudget >= playerPrice && currentPlayerCount < maxPlayers;
    }
    
    public void addPlayer(Player player) {
        if (canBuyPlayer(player.getSoldPrice())) {
            players.add(player);
            remainingBudget -= player.getSoldPrice();
            currentPlayerCount++;
            player.setTeamId(this.teamId);
        }
    }
    
    public void removePlayer(Player player) {
        if (players.remove(player)) {
            remainingBudget += player.getSoldPrice();
            currentPlayerCount--;
            player.setTeamId(-1);
        }
    }
    
    public int getPlayerCountByRole(String role) {
        return (int) players.stream().filter(p -> p.getRole().equals(role)).count();
    }
    
    public double getTotalSpent() {
        return budget - remainingBudget;
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - Budget: ₹%.2f Cr, Remaining: ₹%.2f Cr, Players: %d/%d", 
                           teamName, city, budget, remainingBudget, currentPlayerCount, maxPlayers);
    }
    
    public String getTeamSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Team: ").append(teamName).append(" (").append(city).append(")\n");
        summary.append("Owner: ").append(owner).append("\n");
        summary.append("Coach: ").append(coach != null ? coach : "Not assigned").append("\n");
        summary.append("Captain: ").append(captain != null ? captain : "Not assigned").append("\n");
        summary.append("Budget: ₹").append(budget).append(" Cr\n");
        summary.append("Remaining: ₹").append(remainingBudget).append(" Cr\n");
        summary.append("Players: ").append(currentPlayerCount).append("/").append(maxPlayers).append("\n");
        summary.append("Total Spent: ₹").append(getTotalSpent()).append(" Cr");
        return summary.toString();
    }
}
