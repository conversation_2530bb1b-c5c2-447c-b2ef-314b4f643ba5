package models;

/**
 * Player model representing an IPL player
 */
public class Player {
    private int playerId;
    private String name;
    private String country;
    private String role; // <PERSON><PERSON>, Bowler, All-rounder, Wicket-keeper
    private int age;
    private double basePrice;
    private String category; // Capped, Uncapped
    private boolean isSold;
    private int teamId;
    private double soldPrice;
    private String specialSkills;
    
    // Constructors
    public Player() {}
    
    public Player(String name, String country, String role, int age, double basePrice, String category) {
        this.name = name;
        this.country = country;
        this.role = role;
        this.age = age;
        this.basePrice = basePrice;
        this.category = category;
        this.isSold = false;
        this.teamId = -1;
        this.soldPrice = 0.0;
    }
    
    public Player(int playerId, String name, String country, String role, int age, 
                  double basePrice, String category, boolean isSold, int teamId, 
                  double soldPrice, String specialSkills) {
        this.playerId = playerId;
        this.name = name;
        this.country = country;
        this.role = role;
        this.age = age;
        this.basePrice = basePrice;
        this.category = category;
        this.isSold = isSold;
        this.teamId = teamId;
        this.soldPrice = soldPrice;
        this.specialSkills = specialSkills;
    }
    
    // Getters and Setters
    public int getPlayerId() { return playerId; }
    public void setPlayerId(int playerId) { this.playerId = playerId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }
    
    public int getAge() { return age; }
    public void setAge(int age) { this.age = age; }
    
    public double getBasePrice() { return basePrice; }
    public void setBasePrice(double basePrice) { this.basePrice = basePrice; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public boolean isSold() { return isSold; }
    public void setSold(boolean sold) { isSold = sold; }
    
    public int getTeamId() { return teamId; }
    public void setTeamId(int teamId) { this.teamId = teamId; }
    
    public double getSoldPrice() { return soldPrice; }
    public void setSoldPrice(double soldPrice) { this.soldPrice = soldPrice; }
    
    public String getSpecialSkills() { return specialSkills; }
    public void setSpecialSkills(String specialSkills) { this.specialSkills = specialSkills; }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - %s, Age: %d, Base Price: ₹%.2f Cr", 
                           name, country, role, age, basePrice);
    }
    
    public String getDetailedInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Name: ").append(name).append("\n");
        info.append("Country: ").append(country).append("\n");
        info.append("Role: ").append(role).append("\n");
        info.append("Age: ").append(age).append("\n");
        info.append("Category: ").append(category).append("\n");
        info.append("Base Price: ₹").append(basePrice).append(" Cr\n");
        if (specialSkills != null && !specialSkills.isEmpty()) {
            info.append("Special Skills: ").append(specialSkills).append("\n");
        }
        if (isSold) {
            info.append("Status: SOLD for ₹").append(soldPrice).append(" Cr");
        } else {
            info.append("Status: UNSOLD");
        }
        return info.toString();
    }
}
