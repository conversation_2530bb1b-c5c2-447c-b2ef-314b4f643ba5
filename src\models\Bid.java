package models;

import java.time.LocalDateTime;

/**
 * Bid model representing a bid placed during auction
 */
public class Bid {
    private int bidId;
    private int auctionId;
    private int playerId;
    private int teamId;
    private double bidAmount;
    private LocalDateTime bidTime;
    private String bidStatus; // ACTIVE, OUTBID, WINNING, WITHDRAWN
    private String bidType; // INITIAL, INCREMENT, FINAL
    
    // Constructors
    public Bid() {}
    
    public Bid(int auctionId, int playerId, int teamId, double bidAmount, String bidType) {
        this.auctionId = auctionId;
        this.playerId = playerId;
        this.teamId = teamId;
        this.bidAmount = bidAmount;
        this.bidType = bidType;
        this.bidTime = LocalDateTime.now();
        this.bidStatus = "ACTIVE";
    }
    
    public Bid(int bidId, int auctionId, int playerId, int teamId, double bidAmount, 
               LocalDateTime bidTime, String bidStatus, String bidType) {
        this.bidId = bidId;
        this.auctionId = auctionId;
        this.playerId = playerId;
        this.teamId = teamId;
        this.bidAmount = bidAmount;
        this.bidTime = bidTime;
        this.bidStatus = bidStatus;
        this.bidType = bidType;
    }
    
    // Getters and Setters
    public int getBidId() { return bidId; }
    public void setBidId(int bidId) { this.bidId = bidId; }
    
    public int getAuctionId() { return auctionId; }
    public void setAuctionId(int auctionId) { this.auctionId = auctionId; }
    
    public int getPlayerId() { return playerId; }
    public void setPlayerId(int playerId) { this.playerId = playerId; }
    
    public int getTeamId() { return teamId; }
    public void setTeamId(int teamId) { this.teamId = teamId; }
    
    public double getBidAmount() { return bidAmount; }
    public void setBidAmount(double bidAmount) { this.bidAmount = bidAmount; }
    
    public LocalDateTime getBidTime() { return bidTime; }
    public void setBidTime(LocalDateTime bidTime) { this.bidTime = bidTime; }
    
    public String getBidStatus() { return bidStatus; }
    public void setBidStatus(String bidStatus) { this.bidStatus = bidStatus; }
    
    public String getBidType() { return bidType; }
    public void setBidType(String bidType) { this.bidType = bidType; }
    
    // Business methods
    public boolean isActive() {
        return "ACTIVE".equals(bidStatus);
    }
    
    public boolean isWinning() {
        return "WINNING".equals(bidStatus);
    }
    
    public boolean isOutbid() {
        return "OUTBID".equals(bidStatus);
    }
    
    public boolean isWithdrawn() {
        return "WITHDRAWN".equals(bidStatus);
    }
    
    public void markAsOutbid() {
        this.bidStatus = "OUTBID";
    }
    
    public void markAsWinning() {
        this.bidStatus = "WINNING";
    }
    
    public void withdraw() {
        this.bidStatus = "WITHDRAWN";
    }
    
    @Override
    public String toString() {
        return String.format("Bid: ₹%.2f Cr by Team %d for Player %d (%s)", 
                           bidAmount, teamId, playerId, bidStatus);
    }
    
    public String getBidDetails() {
        StringBuilder details = new StringBuilder();
        details.append("Bid ID: ").append(bidId).append("\n");
        details.append("Auction ID: ").append(auctionId).append("\n");
        details.append("Player ID: ").append(playerId).append("\n");
        details.append("Team ID: ").append(teamId).append("\n");
        details.append("Bid Amount: ₹").append(bidAmount).append(" Cr\n");
        details.append("Bid Time: ").append(bidTime).append("\n");
        details.append("Status: ").append(bidStatus).append("\n");
        details.append("Type: ").append(bidType);
        return details.toString();
    }
}
