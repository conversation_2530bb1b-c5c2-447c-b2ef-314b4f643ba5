@echo off
echo IPL Auction System - Build and Run Script
echo ==========================================

REM Create bin directory if it doesn't exist
if not exist "bin" mkdir bin

echo.
echo Compiling Java files...
javac -cp "lib/*;src" -d bin src/IPLAuctionApp.java src/models/*.java src/config/*.java src/database/*.java src/dao/*.java src/services/*.java src/gui/*.java

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Compilation failed! Please check for errors.
    pause
    exit /b 1
)

echo Compilation successful!
echo.
echo Starting IPL Auction System...
echo.

REM Run the application
java -cp "lib/*;bin" IPLAuctionApp

echo.
echo Application closed.
pause
