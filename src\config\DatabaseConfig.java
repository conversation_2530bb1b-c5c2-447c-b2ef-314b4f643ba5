package config;

/**
 * Database configuration class containing connection parameters
 */
public class DatabaseConfig {
    // Database connection parameters
    public static final String DB_URL = "***************************************";
    public static final String DB_USERNAME = "root";
    public static final String DB_PASSWORD = "M@noj4188"; // Change this to your MySQL password
    public static final String DB_DRIVER = "com.mysql.cj.jdbc.Driver";
    
    // Database name
    public static final String DB_NAME = "ipl_auction";
    
    // Connection pool settings
    public static final int MAX_CONNECTIONS = 10;
    public static final int CONNECTION_TIMEOUT = 30000; // 30 seconds
    
    // Table names
    public static final String TABLE_PLAYERS = "players";
    public static final String TABLE_TEAMS = "teams";
    public static final String TABLE_AUCTIONS = "auctions";
    public static final String TABLE_BIDS = "bids";
    
    // Default values
    public static final double DEFAULT_TEAM_BUDGET = 100.0; // 100 Crores
    public static final int DEFAULT_MAX_PLAYERS = 25;
    public static final double MIN_BID_INCREMENT = 0.25; // 25 Lakhs
    
    // Player categories
    public static final String[] PLAYER_CATEGORIES = {"Capped", "Uncapped"};
    
    // Player roles
    public static final String[] PLAYER_ROLES = {
        "Batsman", "Bowler", "All-rounder", "Wicket-keeper"
    };
    
    // Auction statuses
    public static final String[] AUCTION_STATUSES = {
        "SCHEDULED", "ACTIVE", "PAUSED", "COMPLETED"
    };
    
    // Bid statuses
    public static final String[] BID_STATUSES = {
        "ACTIVE", "OUTBID", "WINNING", "WITHDRAWN"
    };
    
    // Bid types
    public static final String[] BID_TYPES = {
        "INITIAL", "INCREMENT", "FINAL"
    };
    
    // Countries (common cricket playing nations)
    public static final String[] COUNTRIES = {
        "India", "Australia", "England", "South Africa", "New Zealand", 
        "West Indies", "Pakistan", "Sri Lanka", "Bangladesh", "Afghanistan",
        "Zimbabwe", "Ireland", "Netherlands", "Scotland", "USA"
    };
    
    // IPL team cities and names (for reference)
    public static final String[][] DEFAULT_TEAMS = {
        {"Mumbai Indians", "Mumbai", "Reliance Industries"},
        {"Chennai Super Kings", "Chennai", "Chennai Super Kings Cricket Ltd"},
        {"Royal Challengers Bangalore", "Bangalore", "United Spirits"},
        {"Kolkata Knight Riders", "Kolkata", "Red Chillies Entertainment"},
        {"Delhi Capitals", "Delhi", "JSW Group"},
        {"Punjab Kings", "Punjab", "Mohit Burman"},
        {"Rajasthan Royals", "Rajasthan", "Manoj Badale"},
        {"Sunrisers Hyderabad", "Hyderabad", "Sun TV Network"}
    };
    
    // Utility methods
    public static String getCreateDatabaseQuery() {
        return "CREATE DATABASE IF NOT EXISTS " + DB_NAME + 
               " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    }
    
    public static String getUseDatabaseQuery() {
        return "USE " + DB_NAME;
    }
    
    // Validation methods
    public static boolean isValidPlayerRole(String role) {
        for (String validRole : PLAYER_ROLES) {
            if (validRole.equals(role)) {
                return true;
            }
        }
        return false;
    }
    
    public static boolean isValidPlayerCategory(String category) {
        for (String validCategory : PLAYER_CATEGORIES) {
            if (validCategory.equals(category)) {
                return true;
            }
        }
        return false;
    }
    
    public static boolean isValidCountry(String country) {
        for (String validCountry : COUNTRIES) {
            if (validCountry.equals(country)) {
                return true;
            }
        }
        return false;
    }
    
    public static boolean isValidAuctionStatus(String status) {
        for (String validStatus : AUCTION_STATUSES) {
            if (validStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
    
    public static boolean isValidBidStatus(String status) {
        for (String validStatus : BID_STATUSES) {
            if (validStatus.equals(status)) {
                return true;
            }
        }
        return false;
    }
}
