package services;

import dao.AuctionDAO;
import dao.PlayerDAO;
import dao.TeamDAO;
import models.Auction;
import models.Bid;
import models.Player;
import models.Team;
import config.DatabaseConfig;
import java.util.List;
import java.util.logging.Logger;

/**
 * Service class for auction business logic
 */
public class AuctionService {
    private static final Logger LOGGER = Logger.getLogger(AuctionService.class.getName());
    
    private AuctionDAO auctionDAO;
    private PlayerDAO playerDAO;
    private TeamDAO teamDAO;
    
    public AuctionService() {
        this.auctionDAO = new AuctionDAO();
        this.playerDAO = new PlayerDAO();
        this.teamDAO = new TeamDAO();
    }
    
    /**
     * Create a new auction
     */
    public boolean createAuction(String auctionName, String auctionType, String description) {
        // Check if there's already an active auction
        Auction activeAuction = auctionDAO.getActiveAuction();
        if (activeAuction != null) {
            LOGGER.warning("Cannot create auction - there's already an active auction: " + activeAuction.getAuctionName());
            return false;
        }
        
        Auction auction = new Auction(auctionName, auctionType, description);
        return auctionDAO.createAuction(auction);
    }
    
    /**
     * Start an auction
     */
    public boolean startAuction(int auctionId) {
        Auction auction = auctionDAO.getAuctionById(auctionId);
        if (auction == null) {
            LOGGER.warning("Auction not found: " + auctionId);
            return false;
        }
        
        if (!auction.isScheduled()) {
            LOGGER.warning("Auction cannot be started - current status: " + auction.getStatus());
            return false;
        }
        
        return auctionDAO.updateAuctionStatus(auctionId, "ACTIVE");
    }
    
    /**
     * Pause an auction
     */
    public boolean pauseAuction(int auctionId) {
        return auctionDAO.updateAuctionStatus(auctionId, "PAUSED");
    }
    
    /**
     * Resume an auction
     */
    public boolean resumeAuction(int auctionId) {
        return auctionDAO.updateAuctionStatus(auctionId, "ACTIVE");
    }
    
    /**
     * Complete an auction
     */
    public boolean completeAuction(int auctionId) {
        return auctionDAO.updateAuctionStatus(auctionId, "COMPLETED");
    }
    
    /**
     * Set current player for auction
     */
    public boolean setCurrentPlayer(int auctionId, int playerId) {
        Player player = playerDAO.getPlayerById(playerId);
        if (player == null) {
            LOGGER.warning("Player not found: " + playerId);
            return false;
        }
        
        if (player.isSold()) {
            LOGGER.warning("Player already sold: " + player.getName());
            return false;
        }
        
        return auctionDAO.updateCurrentPlayer(auctionId, playerId, player.getBasePrice());
    }
    
    /**
     * Place a bid
     */
    public boolean placeBid(int auctionId, int playerId, int teamId, double bidAmount) {
        // Validate auction
        Auction auction = auctionDAO.getAuctionById(auctionId);
        if (auction == null || !auction.isActive()) {
            LOGGER.warning("Auction is not active: " + auctionId);
            return false;
        }
        
        // Validate player
        Player player = playerDAO.getPlayerById(playerId);
        if (player == null || player.isSold()) {
            LOGGER.warning("Player not available for bidding: " + playerId);
            return false;
        }
        
        // Validate team
        Team team = teamDAO.getTeamById(teamId);
        if (team == null) {
            LOGGER.warning("Team not found: " + teamId);
            return false;
        }
        
        // Check if team can afford the bid
        if (!team.canBuyPlayer(bidAmount)) {
            LOGGER.warning("Team cannot afford bid: " + team.getTeamName() + " - Bid: " + bidAmount);
            return false;
        }
        
        // Check minimum bid amount
        double minimumBid = Math.max(player.getBasePrice(), auction.getCurrentBid() + DatabaseConfig.MIN_BID_INCREMENT);
        if (bidAmount < minimumBid) {
            LOGGER.warning("Bid amount too low. Minimum: " + minimumBid + ", Actual: " + bidAmount);
            return false;
        }
        
        // Create and place bid
        Bid bid = new Bid(auctionId, playerId, teamId, bidAmount, "INCREMENT");
        return auctionDAO.placeBid(bid);
    }
    
    /**
     * Sell player to highest bidder
     */
    public boolean sellPlayer(int auctionId, int playerId) {
        // Get highest bid
        Bid highestBid = auctionDAO.getHighestBidForPlayer(auctionId, playerId);
        if (highestBid == null) {
            LOGGER.warning("No bids found for player: " + playerId);
            return false;
        }
        
        // Mark bid as winning
        if (!auctionDAO.markWinningBid(auctionId, playerId)) {
            LOGGER.warning("Failed to mark winning bid");
            return false;
        }
        
        // Update player as sold
        if (!playerDAO.sellPlayer(playerId, highestBid.getTeamId(), highestBid.getBidAmount())) {
            LOGGER.warning("Failed to mark player as sold");
            return false;
        }
        
        // Update team budget
        Team team = teamDAO.getTeamById(highestBid.getTeamId());
        if (team != null) {
            double newBudget = team.getRemainingBudget() - highestBid.getBidAmount();
            int newPlayerCount = team.getCurrentPlayerCount() + 1;
            teamDAO.updateTeamBudget(highestBid.getTeamId(), newBudget, newPlayerCount);
        }
        
        LOGGER.info("Player sold successfully: " + playerId + " to team " + highestBid.getTeamId() + 
                   " for ₹" + highestBid.getBidAmount() + " Cr");
        return true;
    }
    
    /**
     * Mark player as unsold
     */
    public boolean markPlayerUnsold(int playerId) {
        return playerDAO.unsellPlayer(playerId);
    }
    
    /**
     * Get next unsold player
     */
    public Player getNextUnsoldPlayer() {
        List<Player> unsoldPlayers = playerDAO.getUnsoldPlayers();
        return unsoldPlayers.isEmpty() ? null : unsoldPlayers.get(0);
    }
    
    /**
     * Get auction summary
     */
    public String getAuctionSummary(int auctionId) {
        Auction auction = auctionDAO.getAuctionById(auctionId);
        if (auction == null) {
            return "Auction not found";
        }
        
        List<Player> soldPlayers = playerDAO.getSoldPlayers();
        List<Player> unsoldPlayers = playerDAO.getUnsoldPlayers();
        
        StringBuilder summary = new StringBuilder();
        summary.append("=== AUCTION SUMMARY ===\n");
        summary.append("Auction: ").append(auction.getAuctionName()).append("\n");
        summary.append("Status: ").append(auction.getStatus()).append("\n");
        summary.append("Players Sold: ").append(soldPlayers.size()).append("\n");
        summary.append("Players Unsold: ").append(unsoldPlayers.size()).append("\n");
        
        double totalSpent = soldPlayers.stream().mapToDouble(Player::getSoldPrice).sum();
        summary.append("Total Amount Spent: ₹").append(totalSpent).append(" Cr\n");
        
        if (!soldPlayers.isEmpty()) {
            double avgPrice = totalSpent / soldPlayers.size();
            summary.append("Average Player Price: ₹").append(String.format("%.2f", avgPrice)).append(" Cr\n");
            
            Player mostExpensive = soldPlayers.stream()
                .max((p1, p2) -> Double.compare(p1.getSoldPrice(), p2.getSoldPrice()))
                .orElse(null);
            if (mostExpensive != null) {
                summary.append("Most Expensive: ").append(mostExpensive.getName())
                       .append(" - ₹").append(mostExpensive.getSoldPrice()).append(" Cr\n");
            }
        }
        
        return summary.toString();
    }
    
    /**
     * Get team-wise auction results
     */
    public String getTeamWiseResults() {
        List<Team> teams = teamDAO.getAllTeams();
        StringBuilder results = new StringBuilder();
        results.append("=== TEAM-WISE RESULTS ===\n\n");
        
        for (Team team : teams) {
            results.append("Team: ").append(team.getTeamName()).append("\n");
            results.append("Budget Used: ₹").append(team.getTotalSpent()).append(" Cr\n");
            results.append("Remaining: ₹").append(team.getRemainingBudget()).append(" Cr\n");
            results.append("Players: ").append(team.getCurrentPlayerCount()).append("/").append(team.getMaxPlayers()).append("\n");
            
            List<Player> teamPlayers = playerDAO.getPlayersByTeam(team.getTeamId());
            if (!teamPlayers.isEmpty()) {
                results.append("Squad:\n");
                for (Player player : teamPlayers) {
                    results.append("  - ").append(player.getName())
                           .append(" (").append(player.getRole()).append(") - ₹")
                           .append(player.getSoldPrice()).append(" Cr\n");
                }
            }
            results.append("\n");
        }
        
        return results.toString();
    }
    
    /**
     * Reset auction (mark all players as unsold)
     */
    public boolean resetAuction() {
        try {
            List<Player> soldPlayers = playerDAO.getSoldPlayers();
            for (Player player : soldPlayers) {
                playerDAO.unsellPlayer(player.getPlayerId());
            }
            
            // Reset team budgets
            List<Team> teams = teamDAO.getAllTeams();
            for (Team team : teams) {
                teamDAO.resetTeamForNewAuction(team.getTeamId(), DatabaseConfig.DEFAULT_TEAM_BUDGET);
            }
            
            LOGGER.info("Auction reset successfully");
            return true;
        } catch (Exception e) {
            LOGGER.severe("Error resetting auction: " + e.getMessage());
            return false;
        }
    }
    
    // Getter methods for DAOs (for GUI access)
    public AuctionDAO getAuctionDAO() { return auctionDAO; }
    public PlayerDAO getPlayerDAO() { return playerDAO; }
    public TeamDAO getTeamDAO() { return teamDAO; }
}
