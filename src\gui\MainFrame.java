package gui;

import database.DatabaseConnection;
import database.DatabaseSetup;
import services.AuctionService;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.logging.Logger;

/**
 * Main application window for IPL Auction System
 */
public class MainFrame extends JFrame {
    private static final Logger LOGGER = Logger.getLogger(MainFrame.class.getName());
    
    private AuctionService auctionService;
    private JTabbedPane tabbedPane;
    private PlayerManagementPanel playerPanel;
    private TeamManagementPanel teamPanel;
    private AuctionPanel auctionPanel;
    private ResultsPanel resultsPanel;
    private JLabel statusLabel;
    
    public MainFrame() {
        initializeServices();
        initializeGUI();
        setupEventHandlers();
    }
    
    /**
     * Initialize services and database
     */
    private void initializeServices() {
        try {
            // Test database connection
            DatabaseConnection dbConnection = DatabaseConnection.getInstance();
            if (!dbConnection.testConnection()) {
                showErrorDialog("Database Connection Failed", 
                    "Could not connect to MySQL database. Please check your configuration.");
                System.exit(1);
            }
            
            // Initialize database if needed
            DatabaseSetup dbSetup = new DatabaseSetup();
            if (!dbSetup.isDatabaseInitialized()) {
                int result = JOptionPane.showConfirmDialog(this,
                    "Database not initialized. Would you like to set it up now?",
                    "Database Setup", JOptionPane.YES_NO_OPTION);
                
                if (result == JOptionPane.YES_OPTION) {
                    if (!dbSetup.initializeDatabase()) {
                        showErrorDialog("Database Setup Failed", 
                            "Could not initialize database. Please check your MySQL configuration.");
                        System.exit(1);
                    }
                    JOptionPane.showMessageDialog(this, 
                        "Database initialized successfully with sample data!");
                } else {
                    System.exit(0);
                }
            }
            
            // Initialize auction service
            this.auctionService = new AuctionService();
            LOGGER.info("Services initialized successfully");
            
        } catch (Exception e) {
            LOGGER.severe("Error initializing services: " + e.getMessage());
            showErrorDialog("Initialization Error", 
                "Failed to initialize application: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * Initialize GUI components
     */
    private void initializeGUI() {
        setTitle("IPL Auction System");
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // Set look and feel
        try {
            // Try to set Nimbus look and feel
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            LOGGER.warning("Could not set look and feel");
        }
        
        // Create menu bar
        createMenuBar();
        
        // Create main panel
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // Create tabbed pane
        tabbedPane = new JTabbedPane();
        
        // Initialize panels
        playerPanel = new PlayerManagementPanel(auctionService);
        teamPanel = new TeamManagementPanel(auctionService);
        auctionPanel = new AuctionPanel(auctionService);
        resultsPanel = new ResultsPanel(auctionService);
        
        // Add tabs
        tabbedPane.addTab("Players", new ImageIcon(), playerPanel, "Manage Players");
        tabbedPane.addTab("Teams", new ImageIcon(), teamPanel, "Manage Teams");
        tabbedPane.addTab("Auction", new ImageIcon(), auctionPanel, "Live Auction");
        tabbedPane.addTab("Results", new ImageIcon(), resultsPanel, "Auction Results");
        
        mainPanel.add(tabbedPane, BorderLayout.CENTER);
        
        // Create status bar
        createStatusBar();
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Set initial status
        updateStatus("Application started successfully");
    }
    
    /**
     * Create menu bar
     */
    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        
        // File menu
        JMenu fileMenu = new JMenu("File");
        
        JMenuItem newAuctionItem = new JMenuItem("New Auction");
        newAuctionItem.addActionListener(e -> showNewAuctionDialog());
        fileMenu.add(newAuctionItem);
        
        fileMenu.addSeparator();
        
        JMenuItem resetItem = new JMenuItem("Reset Auction");
        resetItem.addActionListener(e -> resetAuction());
        fileMenu.add(resetItem);
        
        fileMenu.addSeparator();
        
        JMenuItem exitItem = new JMenuItem("Exit");
        exitItem.addActionListener(e -> exitApplication());
        fileMenu.add(exitItem);
        
        // Tools menu
        JMenu toolsMenu = new JMenu("Tools");
        
        JMenuItem dbStatusItem = new JMenuItem("Database Status");
        dbStatusItem.addActionListener(e -> showDatabaseStatus());
        toolsMenu.add(dbStatusItem);
        
        JMenuItem refreshItem = new JMenuItem("Refresh All");
        refreshItem.addActionListener(e -> refreshAllPanels());
        toolsMenu.add(refreshItem);
        
        // Help menu
        JMenu helpMenu = new JMenu("Help");
        
        JMenuItem aboutItem = new JMenuItem("About");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        menuBar.add(fileMenu);
        menuBar.add(toolsMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    /**
     * Create status bar
     */
    private void createStatusBar() {
        statusLabel = new JLabel("Ready");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        statusLabel.setPreferredSize(new Dimension(getWidth(), 25));
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Window closing event
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                exitApplication();
            }
        });
        
        // Tab change event
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            String tabName = tabbedPane.getTitleAt(selectedIndex);
            updateStatus("Switched to " + tabName + " tab");
            
            // Refresh the selected panel
            refreshSelectedPanel();
        });
    }
    
    /**
     * Show new auction dialog
     */
    private void showNewAuctionDialog() {
        JDialog dialog = new JDialog(this, "Create New Auction", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(this);
        
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // Auction name
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("Auction Name:"), gbc);
        gbc.gridx = 1;
        JTextField nameField = new JTextField(20);
        panel.add(nameField, gbc);
        
        // Auction type
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("Type:"), gbc);
        gbc.gridx = 1;
        JComboBox<String> typeCombo = new JComboBox<>(new String[]{"PLAYER", "RETENTION"});
        panel.add(typeCombo, gbc);
        
        // Description
        gbc.gridx = 0; gbc.gridy = 2;
        panel.add(new JLabel("Description:"), gbc);
        gbc.gridx = 1;
        JTextArea descArea = new JTextArea(3, 20);
        descArea.setLineWrap(true);
        panel.add(new JScrollPane(descArea), gbc);
        
        // Buttons
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 2;
        JPanel buttonPanel = new JPanel();
        JButton createButton = new JButton("Create");
        JButton cancelButton = new JButton("Cancel");
        
        createButton.addActionListener(e -> {
            String name = nameField.getText().trim();
            String type = (String) typeCombo.getSelectedItem();
            String description = descArea.getText().trim();
            
            if (name.isEmpty()) {
                JOptionPane.showMessageDialog(dialog, "Please enter auction name");
                return;
            }
            
            if (auctionService.createAuction(name, type, description)) {
                JOptionPane.showMessageDialog(dialog, "Auction created successfully!");
                dialog.dispose();
                refreshAllPanels();
                updateStatus("New auction created: " + name);
            } else {
                JOptionPane.showMessageDialog(dialog, "Failed to create auction");
            }
        });
        
        cancelButton.addActionListener(e -> dialog.dispose());
        
        buttonPanel.add(createButton);
        buttonPanel.add(cancelButton);
        panel.add(buttonPanel, gbc);
        
        dialog.add(panel);
        dialog.setVisible(true);
    }
    
    /**
     * Reset auction
     */
    private void resetAuction() {
        int result = JOptionPane.showConfirmDialog(this,
            "This will reset all auction data. Are you sure?",
            "Reset Auction", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (auctionService.resetAuction()) {
                JOptionPane.showMessageDialog(this, "Auction reset successfully!");
                refreshAllPanels();
                updateStatus("Auction reset completed");
            } else {
                showErrorDialog("Reset Failed", "Could not reset auction data");
            }
        }
    }
    
    /**
     * Show database status
     */
    private void showDatabaseStatus() {
        DatabaseConnection dbConnection = DatabaseConnection.getInstance();
        boolean connected = dbConnection.testConnection();
        
        String message = connected ? 
            "Database connection is active" : 
            "Database connection failed";
        
        JOptionPane.showMessageDialog(this, message, "Database Status", 
            connected ? JOptionPane.INFORMATION_MESSAGE : JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * Show about dialog
     */
    private void showAboutDialog() {
        String message = """
            IPL Auction System
            Version 1.0
            
            A comprehensive auction management system for IPL teams.
            
            Features:
            • Player Management
            • Team Management  
            • Live Auction
            • Results & Analytics
            
            Built with Java Swing and MySQL
            """;
        
        JOptionPane.showMessageDialog(this, message, "About", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * Refresh all panels
     */
    private void refreshAllPanels() {
        playerPanel.refreshData();
        teamPanel.refreshData();
        auctionPanel.refreshData();
        resultsPanel.refreshData();
        updateStatus("All panels refreshed");
    }
    
    /**
     * Refresh selected panel
     */
    private void refreshSelectedPanel() {
        int selectedIndex = tabbedPane.getSelectedIndex();
        switch (selectedIndex) {
            case 0 -> playerPanel.refreshData();
            case 1 -> teamPanel.refreshData();
            case 2 -> auctionPanel.refreshData();
            case 3 -> resultsPanel.refreshData();
        }
    }
    
    /**
     * Update status bar
     */
    public void updateStatus(String message) {
        statusLabel.setText(message);
        LOGGER.info("Status: " + message);
    }
    
    /**
     * Show error dialog
     */
    private void showErrorDialog(String title, String message) {
        JOptionPane.showMessageDialog(this, message, title, JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * Exit application
     */
    private void exitApplication() {
        int result = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to exit?", "Exit Application",
            JOptionPane.YES_NO_OPTION);
        
        if (result == JOptionPane.YES_OPTION) {
            // Close database connection
            DatabaseConnection.getInstance().closeConnection();
            System.exit(0);
        }
    }
}
