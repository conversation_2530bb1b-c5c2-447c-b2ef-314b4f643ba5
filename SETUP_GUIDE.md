# IPL Auction System - Setup Guide

## Quick Start

### Prerequisites
1. **Java 11 or higher** - Download from [Oracle](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://openjdk.org/)
2. **MySQL 8.0 or higher** - Download from [MySQL](https://dev.mysql.com/downloads/mysql/)

### Step 1: Database Setup

1. **Install and Start MySQL**
   - Install MySQL Server on your system
   - Start the MySQL service
   - Note down your root password

2. **Create Database**
   - Open MySQL Command Line Client or MySQL Workbench
   - Run the provided SQL script:
   ```bash
   mysql -u root -p < database_schema.sql
   ```
   - Or copy and paste the contents of `database_schema.sql` into your MySQL client

3. **Verify Database Creation**
   ```sql
   USE ipl_auction;
   SHOW TABLES;
   SELECT COUNT(*) FROM teams;
   SELECT COUNT(*) FROM players;
   ```

### Step 2: Configure Database Connection

1. **Open Configuration File**
   - Navigate to `src/config/DatabaseConfig.java`

2. **Update Database Credentials**
   ```java
   public static final String DB_URL = "***************************************";
   public static final String DB_USERNAME = "root";  // Your MySQL username
   public static final String DB_PASSWORD = "your_password_here";  // Your MySQL password
   ```

### Step 3: Run the Application

#### Option A: Using Batch/Shell Scripts (Recommended)

**Windows:**
```bash
run.bat
```

**Linux/Mac:**
```bash
chmod +x run.sh
./run.sh
```

#### Option B: Manual Compilation and Execution

**Windows:**
```bash
# Compile
javac -cp "lib/*;src" -d bin src/IPLAuctionApp.java src/models/*.java src/config/*.java src/database/*.java src/dao/*.java src/services/*.java src/gui/*.java

# Run
java -cp "lib/*;bin" IPLAuctionApp
```

**Linux/Mac:**
```bash
# Compile
javac -cp "lib/*:src" -d bin src/IPLAuctionApp.java src/models/*.java src/config/*.java src/database/*.java src/dao/*.java src/services/*.java src/gui/*.java

# Run
java -cp "lib/*:bin" IPLAuctionApp
```

### Step 4: First Time Setup

1. **Launch Application**
   - The application will show a splash screen
   - If database is not initialized, you'll be prompted to set it up

2. **Initialize Database**
   - Click "Yes" when prompted to initialize database
   - This will create tables and insert sample data

3. **Start Using the Application**
   - The main window will open with four tabs:
     - **Players**: Manage player information
     - **Teams**: Manage team details and budgets
     - **Auction**: Conduct live auctions
     - **Results**: View auction results and analytics

## Detailed Usage Instructions

### Managing Players

1. **Add New Players**
   - Go to Players tab
   - Click "Add Player"
   - Fill in player details (name, country, role, age, base price, category)
   - Add special skills if any
   - Click "Add Player"

2. **Search and Filter**
   - Use the search box to find players by name
   - Filter by role, country, or status
   - Click "Search" or press Enter

3. **Edit/Delete Players**
   - Select a player from the table
   - Click "Edit Player" to modify details
   - Click "Delete Player" to remove (only if not sold)

### Managing Teams

1. **View Team Information**
   - Go to Teams tab
   - See all teams with budget and player count
   - Select a team to view their current squad

2. **Add New Teams**
   - Click "Add Team"
   - Enter team name, city, owner
   - Set budget and maximum players
   - Add coach and captain if known

3. **Team Statistics**
   - Select a team and click "View Statistics"
   - See detailed spending and composition analysis

### Conducting Auctions

1. **Create New Auction**
   - Go to File → New Auction
   - Enter auction name and description
   - Choose auction type (Player/Retention)

2. **Start Auction**
   - Select auction from dropdown
   - Click "Start Auction"
   - Use "Next Player" to bring up players for bidding

3. **Place Bids**
   - Select team from dropdown
   - Enter bid amount (must be higher than current bid)
   - Click "Place Bid"
   - System validates team budget and bid amount

4. **Complete Player Auction**
   - Click "SOLD!" to sell to highest bidder
   - Click "UNSOLD" if no acceptable bids
   - System automatically updates team budgets

### Viewing Results

1. **Sold Players**
   - See all players sold with prices and teams
   - Sort by any column
   - Double-click for player details

2. **Team Summary**
   - View spending and composition for each team
   - Compare team strategies
   - See most expensive players per team

3. **Overall Summary**
   - Get complete auction statistics
   - Total spending, average prices
   - Most/least expensive players

## Troubleshooting

### Common Issues

1. **"Database Connection Failed"**
   - Check if MySQL server is running
   - Verify username/password in DatabaseConfig.java
   - Ensure database 'ipl_auction' exists

2. **"ClassNotFoundException: com.mysql.cj.jdbc.Driver"**
   - Ensure mysql-connector-j-8.2.0.jar is in lib folder
   - Check classpath includes lib/*

3. **"Application won't start"**
   - Verify Java version: `java -version`
   - Check if all .class files are in bin folder
   - Look for error messages in console

4. **"GUI looks strange"**
   - Try different screen resolution
   - Update graphics drivers
   - Application works best on 1920x1080 or higher

### Database Issues

1. **Reset Database**
   ```sql
   DROP DATABASE ipl_auction;
   ```
   Then run database_schema.sql again

2. **Check Data**
   ```sql
   USE ipl_auction;
   SELECT * FROM teams LIMIT 5;
   SELECT * FROM players LIMIT 5;
   ```

3. **Manual Data Entry**
   - Use the application's GUI to add teams/players
   - Or insert directly into database using SQL

### Performance Tips

1. **For Large Datasets**
   - Increase JVM heap size: `java -Xmx2g -cp "lib/*:bin" IPLAuctionApp`
   - Use database indexing (already included in schema)

2. **Network Database**
   - Update DB_URL in DatabaseConfig.java
   - Ensure network connectivity and firewall settings

## Advanced Configuration

### Custom Team Budgets
- Modify `DEFAULT_TEAM_BUDGET` in DatabaseConfig.java
- Or update directly in database: `UPDATE teams SET budget = 120.0, remaining_budget = 120.0;`

### Adding More Countries/Roles
- Update arrays in DatabaseConfig.java
- Modify database ENUM types if needed

### Backup and Restore
```bash
# Backup
mysqldump -u root -p ipl_auction > backup.sql

# Restore
mysql -u root -p ipl_auction < backup.sql
```

## Support

For issues or questions:
1. Check this setup guide
2. Review error messages in console
3. Verify database connectivity
4. Check Java and MySQL versions

The application includes comprehensive logging to help diagnose issues.
