package gui;

import config.DatabaseConfig;
import models.Auction;
import models.Player;
import models.Team;
import models.Bid;
import services.AuctionService;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Panel for live auction interface
 */
public class AuctionPanel extends JPanel {
    private AuctionService auctionService;
    private Timer refreshTimer;
    
    // Current auction components
    private JLabel auctionStatusLabel;
    private JLabel currentPlayerLabel;
    private JLabel currentBidLabel;
    private JLabel currentBidderLabel;
    private JTextArea playerDetailsArea;
    
    // Bidding components
    private JComboBox<String> teamCombo;
    private JSpinner bidAmountSpinner;
    private JButton placeBidButton;
    private JButton sellPlayerButton;
    private JButton unsoldPlayerButton;
    private JButton nextPlayerButton;
    
    // Auction control components
    private J<PERSON>omboBox<String> auctionCombo;
    private JButton startAuctionButton;
    private JButton pauseAuctionButton;
    private JButton resumeAuctionButton;
    private JButton completeAuctionButton;
    
    // Bid history
    private JTable bidHistoryTable;
    private DefaultTableModel bidHistoryModel;
    
    // Current state
    private Auction currentAuction;
    private Player currentPlayer;
    
    public AuctionPanel(AuctionService auctionService) {
        this.auctionService = auctionService;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        startRefreshTimer();
        refreshData();
    }
    
    /**
     * Initialize GUI components
     */
    private void initializeComponents() {
        // Status labels
        auctionStatusLabel = new JLabel("No active auction");
        auctionStatusLabel.setFont(auctionStatusLabel.getFont().deriveFont(Font.BOLD, 16f));
        
        currentPlayerLabel = new JLabel("No player selected");
        currentPlayerLabel.setFont(currentPlayerLabel.getFont().deriveFont(Font.BOLD, 14f));
        
        currentBidLabel = new JLabel("Current Bid: ₹0.00 Cr");
        currentBidLabel.setFont(currentBidLabel.getFont().deriveFont(Font.BOLD, 14f));
        currentBidLabel.setForeground(Color.BLUE);
        
        currentBidderLabel = new JLabel("No bidder");
        
        // Player details
        playerDetailsArea = new JTextArea(5, 30);
        playerDetailsArea.setEditable(false);
        playerDetailsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        // Bidding components
        teamCombo = new JComboBox<>();
        bidAmountSpinner = new JSpinner(new SpinnerNumberModel(0.5, 0.2, 50.0, 0.25));
        
        placeBidButton = new JButton("Place Bid");
        sellPlayerButton = new JButton("SOLD!");
        unsoldPlayerButton = new JButton("UNSOLD");
        nextPlayerButton = new JButton("Next Player");
        
        // Auction control
        auctionCombo = new JComboBox<>();
        startAuctionButton = new JButton("Start Auction");
        pauseAuctionButton = new JButton("Pause");
        resumeAuctionButton = new JButton("Resume");
        completeAuctionButton = new JButton("Complete");
        
        // Bid history table
        String[] bidColumns = {"Time", "Team", "Amount (Cr)", "Status"};
        bidHistoryModel = new DefaultTableModel(bidColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        bidHistoryTable = new JTable(bidHistoryModel);
        bidHistoryTable.setRowHeight(25);
        
        // Set button colors
        sellPlayerButton.setBackground(Color.GREEN);
        sellPlayerButton.setForeground(Color.WHITE);
        unsoldPlayerButton.setBackground(Color.RED);
        unsoldPlayerButton.setForeground(Color.WHITE);
        placeBidButton.setBackground(Color.BLUE);
        placeBidButton.setForeground(Color.WHITE);
    }
    
    /**
     * Setup panel layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Top panel - Auction control
        JPanel topPanel = new JPanel(new FlowLayout());
        topPanel.setBorder(BorderFactory.createTitledBorder("Auction Control"));
        
        topPanel.add(new JLabel("Auction:"));
        topPanel.add(auctionCombo);
        topPanel.add(startAuctionButton);
        topPanel.add(pauseAuctionButton);
        topPanel.add(resumeAuctionButton);
        topPanel.add(completeAuctionButton);
        
        add(topPanel, BorderLayout.NORTH);
        
        // Center panel - Main auction area
        JPanel centerPanel = new JPanel(new BorderLayout());
        
        // Status panel
        JPanel statusPanel = new JPanel(new GridLayout(4, 1, 5, 5));
        statusPanel.setBorder(BorderFactory.createTitledBorder("Auction Status"));
        statusPanel.add(auctionStatusLabel);
        statusPanel.add(currentPlayerLabel);
        statusPanel.add(currentBidLabel);
        statusPanel.add(currentBidderLabel);
        
        centerPanel.add(statusPanel, BorderLayout.NORTH);
        
        // Split pane for player details and bidding
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        
        // Left side - Player details
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setBorder(BorderFactory.createTitledBorder("Current Player"));
        leftPanel.add(new JScrollPane(playerDetailsArea), BorderLayout.CENTER);
        
        JPanel playerButtonPanel = new JPanel(new FlowLayout());
        playerButtonPanel.add(nextPlayerButton);
        leftPanel.add(playerButtonPanel, BorderLayout.SOUTH);
        
        // Right side - Bidding and history
        JPanel rightPanel = new JPanel(new BorderLayout());
        
        // Bidding panel
        JPanel biddingPanel = new JPanel(new GridBagLayout());
        biddingPanel.setBorder(BorderFactory.createTitledBorder("Place Bid"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        gbc.gridx = 0; gbc.gridy = 0;
        biddingPanel.add(new JLabel("Team:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        biddingPanel.add(teamCombo, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        biddingPanel.add(new JLabel("Amount (Cr):"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        biddingPanel.add(bidAmountSpinner, gbc);
        
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        JPanel bidButtonPanel = new JPanel(new FlowLayout());
        bidButtonPanel.add(placeBidButton);
        bidButtonPanel.add(sellPlayerButton);
        bidButtonPanel.add(unsoldPlayerButton);
        biddingPanel.add(bidButtonPanel, gbc);
        
        rightPanel.add(biddingPanel, BorderLayout.NORTH);
        
        // Bid history
        JPanel historyPanel = new JPanel(new BorderLayout());
        historyPanel.setBorder(BorderFactory.createTitledBorder("Bid History"));
        historyPanel.add(new JScrollPane(bidHistoryTable), BorderLayout.CENTER);
        rightPanel.add(historyPanel, BorderLayout.CENTER);
        
        splitPane.setLeftComponent(leftPanel);
        splitPane.setRightComponent(rightPanel);
        splitPane.setDividerLocation(400);
        
        centerPanel.add(splitPane, BorderLayout.CENTER);
        add(centerPanel, BorderLayout.CENTER);
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        startAuctionButton.addActionListener(e -> startAuction());
        pauseAuctionButton.addActionListener(e -> pauseAuction());
        resumeAuctionButton.addActionListener(e -> resumeAuction());
        completeAuctionButton.addActionListener(e -> completeAuction());
        
        nextPlayerButton.addActionListener(e -> selectNextPlayer());
        placeBidButton.addActionListener(e -> placeBid());
        sellPlayerButton.addActionListener(e -> sellCurrentPlayer());
        unsoldPlayerButton.addActionListener(e -> markPlayerUnsold());
        
        auctionCombo.addActionListener(e -> updateAuctionSelection());
    }
    
    /**
     * Start refresh timer
     */
    private void startRefreshTimer() {
        refreshTimer = new Timer(2000, e -> refreshAuctionStatus());
        refreshTimer.start();
    }
    
    /**
     * Refresh data
     */
    public void refreshData() {
        refreshAuctions();
        refreshTeams();
        refreshAuctionStatus();
    }
    
    /**
     * Refresh auctions combo
     */
    private void refreshAuctions() {
        auctionCombo.removeAllItems();
        List<Auction> auctions = auctionService.getAuctionDAO().getAllAuctions();
        
        for (Auction auction : auctions) {
            auctionCombo.addItem(auction.getAuctionName() + " (" + auction.getStatus() + ")");
        }
    }
    
    /**
     * Refresh teams combo
     */
    private void refreshTeams() {
        teamCombo.removeAllItems();
        List<Team> teams = auctionService.getTeamDAO().getAllTeams();
        
        for (Team team : teams) {
            teamCombo.addItem(team.getTeamName() + " (₹" + team.getRemainingBudget() + " Cr)");
        }
    }
    
    /**
     * Refresh auction status
     */
    private void refreshAuctionStatus() {
        currentAuction = auctionService.getAuctionDAO().getActiveAuction();
        
        if (currentAuction != null) {
            auctionStatusLabel.setText("ACTIVE: " + currentAuction.getAuctionName());
            auctionStatusLabel.setForeground(Color.GREEN);
            
            if (currentAuction.getCurrentPlayerId() > 0) {
                currentPlayer = auctionService.getPlayerDAO().getPlayerById(currentAuction.getCurrentPlayerId());
                if (currentPlayer != null) {
                    updateCurrentPlayerDisplay();
                    updateBidHistory();
                }
            }
            
            enableAuctionControls(true);
        } else {
            auctionStatusLabel.setText("No active auction");
            auctionStatusLabel.setForeground(Color.RED);
            currentPlayerLabel.setText("No player selected");
            currentBidLabel.setText("Current Bid: ₹0.00 Cr");
            currentBidderLabel.setText("No bidder");
            playerDetailsArea.setText("");
            
            enableAuctionControls(false);
        }
    }
    
    /**
     * Update current player display
     */
    private void updateCurrentPlayerDisplay() {
        if (currentPlayer != null) {
            currentPlayerLabel.setText("Current Player: " + currentPlayer.getName());
            playerDetailsArea.setText(currentPlayer.getDetailedInfo());
            
            currentBidLabel.setText("Current Bid: ₹" + currentAuction.getCurrentBid() + " Cr");
            
            if (currentAuction.getCurrentBiddingTeamId() > 0) {
                Team biddingTeam = auctionService.getTeamDAO().getTeamById(currentAuction.getCurrentBiddingTeamId());
                currentBidderLabel.setText("Current Bidder: " + (biddingTeam != null ? biddingTeam.getTeamName() : "Unknown"));
            } else {
                currentBidderLabel.setText("No bidder");
            }
            
            // Set minimum bid amount
            double minBid = Math.max(currentPlayer.getBasePrice(), 
                currentAuction.getCurrentBid() + DatabaseConfig.MIN_BID_INCREMENT);
            bidAmountSpinner.setValue(minBid);
        }
    }
    
    /**
     * Update bid history
     */
    private void updateBidHistory() {
        bidHistoryModel.setRowCount(0);
        
        if (currentAuction != null && currentPlayer != null) {
            List<Bid> bids = auctionService.getAuctionDAO().getBidsForPlayer(
                currentAuction.getAuctionId(), currentPlayer.getPlayerId());
            
            for (Bid bid : bids) {
                Team team = auctionService.getTeamDAO().getTeamById(bid.getTeamId());
                Object[] row = {
                    bid.getBidTime().toString().substring(11, 19), // Time only
                    team != null ? team.getTeamName() : "Unknown",
                    "₹" + bid.getBidAmount(),
                    bid.getBidStatus()
                };
                bidHistoryModel.addRow(row);
            }
        }
    }
    
    /**
     * Enable/disable auction controls
     */
    private void enableAuctionControls(boolean hasActiveAuction) {
        placeBidButton.setEnabled(hasActiveAuction && currentPlayer != null);
        sellPlayerButton.setEnabled(hasActiveAuction && currentPlayer != null);
        unsoldPlayerButton.setEnabled(hasActiveAuction && currentPlayer != null);
        nextPlayerButton.setEnabled(hasActiveAuction);
        
        pauseAuctionButton.setEnabled(hasActiveAuction);
        resumeAuctionButton.setEnabled(false); // Will be enabled when paused
        completeAuctionButton.setEnabled(hasActiveAuction);
    }
    
    /**
     * Start auction
     */
    private void startAuction() {
        int selectedIndex = auctionCombo.getSelectedIndex();
        if (selectedIndex == -1) {
            JOptionPane.showMessageDialog(this, "Please select an auction");
            return;
        }
        
        List<Auction> auctions = auctionService.getAuctionDAO().getAllAuctions();
        if (selectedIndex < auctions.size()) {
            Auction auction = auctions.get(selectedIndex);
            if (auctionService.startAuction(auction.getAuctionId())) {
                JOptionPane.showMessageDialog(this, "Auction started successfully!");
                refreshAuctionStatus();
            } else {
                JOptionPane.showMessageDialog(this, "Failed to start auction");
            }
        }
    }
    
    /**
     * Pause auction
     */
    private void pauseAuction() {
        if (currentAuction != null) {
            if (auctionService.pauseAuction(currentAuction.getAuctionId())) {
                JOptionPane.showMessageDialog(this, "Auction paused");
                refreshAuctionStatus();
            }
        }
    }
    
    /**
     * Resume auction
     */
    private void resumeAuction() {
        if (currentAuction != null) {
            if (auctionService.resumeAuction(currentAuction.getAuctionId())) {
                JOptionPane.showMessageDialog(this, "Auction resumed");
                refreshAuctionStatus();
            }
        }
    }
    
    /**
     * Complete auction
     */
    private void completeAuction() {
        if (currentAuction != null) {
            int result = JOptionPane.showConfirmDialog(this,
                "Are you sure you want to complete the auction?",
                "Complete Auction", JOptionPane.YES_NO_OPTION);
            
            if (result == JOptionPane.YES_OPTION) {
                if (auctionService.completeAuction(currentAuction.getAuctionId())) {
                    JOptionPane.showMessageDialog(this, "Auction completed successfully!");
                    refreshAuctionStatus();
                }
            }
        }
    }
    
    /**
     * Select next player
     */
    private void selectNextPlayer() {
        if (currentAuction != null) {
            Player nextPlayer = auctionService.getNextUnsoldPlayer();
            if (nextPlayer != null) {
                if (auctionService.setCurrentPlayer(currentAuction.getAuctionId(), nextPlayer.getPlayerId())) {
                    refreshAuctionStatus();
                } else {
                    JOptionPane.showMessageDialog(this, "Failed to set current player");
                }
            } else {
                JOptionPane.showMessageDialog(this, "No more unsold players available");
            }
        }
    }
    
    /**
     * Place bid
     */
    private void placeBid() {
        if (currentAuction == null || currentPlayer == null) {
            JOptionPane.showMessageDialog(this, "No active auction or player selected");
            return;
        }
        
        int teamIndex = teamCombo.getSelectedIndex();
        if (teamIndex == -1) {
            JOptionPane.showMessageDialog(this, "Please select a team");
            return;
        }
        
        List<Team> teams = auctionService.getTeamDAO().getAllTeams();
        if (teamIndex >= teams.size()) {
            JOptionPane.showMessageDialog(this, "Invalid team selection");
            return;
        }
        
        Team selectedTeam = teams.get(teamIndex);
        double bidAmount = (Double) bidAmountSpinner.getValue();
        
        if (auctionService.placeBid(currentAuction.getAuctionId(), currentPlayer.getPlayerId(), 
                                  selectedTeam.getTeamId(), bidAmount)) {
            refreshAuctionStatus();
            refreshTeams(); // Update team budgets in combo
        } else {
            JOptionPane.showMessageDialog(this, "Failed to place bid. Check team budget and bid amount.");
        }
    }
    
    /**
     * Sell current player
     */
    private void sellCurrentPlayer() {
        if (currentAuction == null || currentPlayer == null) {
            JOptionPane.showMessageDialog(this, "No active auction or player selected");
            return;
        }
        
        if (auctionService.sellPlayer(currentAuction.getAuctionId(), currentPlayer.getPlayerId())) {
            JOptionPane.showMessageDialog(this, "Player sold successfully!");
            refreshAuctionStatus();
            refreshTeams();
            selectNextPlayer(); // Automatically move to next player
        } else {
            JOptionPane.showMessageDialog(this, "Failed to sell player");
        }
    }
    
    /**
     * Mark player as unsold
     */
    private void markPlayerUnsold() {
        if (currentPlayer != null) {
            if (auctionService.markPlayerUnsold(currentPlayer.getPlayerId())) {
                JOptionPane.showMessageDialog(this, "Player marked as unsold");
                selectNextPlayer(); // Move to next player
            } else {
                JOptionPane.showMessageDialog(this, "Failed to mark player as unsold");
            }
        }
    }
    
    /**
     * Update auction selection
     */
    private void updateAuctionSelection() {
        // This method can be used to handle auction selection changes
        refreshAuctionStatus();
    }
    
    /**
     * Cleanup when panel is destroyed
     */
    public void cleanup() {
        if (refreshTimer != null) {
            refreshTimer.stop();
        }
    }
}
